# 数据库结构参考文档

## 📋 概览信息

| 项目 | 值 |
|------|-----|
| **数据库名称** | portfolio_db |
| **总表数量** | 39个 |
| **文档更新时间** | 2025-08-05 |
| **数据库版本** | 清理后版本 |
| **管理工具** | Alembic |

## 📊 表分类统计

| 分类 | 表数量 | 说明 |
|------|--------|------|
| **核心业务表** | 8个 | 博客、图片、标签、用户等核心功能 |
| **配置管理表** | 4个 | 站点配置、SEO设置等 |
| **个人信息表** | 3个 | 教育、职业、导航等个人信息 |
| **内容管理表** | 3个 | 模板、草稿等内容管理 |
| **图标系统表** | 7个 | 图标库、分类、收藏等 |
| **系统监控表** | 2个 | 系统指标、API监控 |
| **时间线系统表** | 2个 | 画廊时间线功能 |
| **布局主题表** | 6个 | 布局、主题、页面配置 |
| **其他表** | 4个 | 关联表、系统表等 |

## 🗂️ 核心业务表（8个）

### 1. blogs - 统一文章系统
**用途**: 博客文章和项目展示的统一管理
**记录数**: 16条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `title` | varchar(255) | 文章标题 | MUL |
| `slug` | varchar(255) | URL友好标识 | UNI |
| `description` | text | 文章描述 | - |
| `author` | varchar(100) | 作者 | - |
| `content` | text | 文章内容 | - |
| `display_date` | datetime | 显示日期 | MUL |
| `published` | tinyint(1) | 是否发布 | - |
| `show_on_homepage` | tinyint(1) | 首页显示 | MUL |
| `article_type` | varchar(50) | 文章类型(blog/project) | MUL |
| `project_url` | varchar(500) | 项目链接 | - |
| `github_url` | varchar(500) | GitHub链接 | - |
| `demo_url` | varchar(500) | 演示链接 | - |
| `logo_url` | varchar(500) | 项目Logo | - |
| `icon` | varchar(100) | 图标名称 | - |
| `project_status` | varchar(50) | 项目状态 | - |
| `is_github_project` | tinyint(1) | 是否GitHub项目 | - |
| `is_open_source` | tinyint(1) | 是否开源 | MUL |
| `featured` | tinyint(1) | 精选标记 | MUL |
| `display_order` | int | 显示顺序 | - |
| `homepage_order` | int | 首页显示顺序 | MUL |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |
| `published_at` | datetime | 发布时间 | - |
| `meta_title` | varchar(255) | SEO标题 | - |
| `meta_description` | text | SEO描述 | - |
| `meta_keywords` | text | SEO关键词 | - |
| `og_title` | varchar(255) | OG标题 | - |
| `og_description` | text | OG描述 | - |
| `og_image` | varchar(500) | OG图片 | - |
| `canonical_url` | varchar(500) | 规范URL | - |

**关联关系**:
- 与 `blog_tags` 多对多关联（通过标签）
- 与 `blog_versions` 一对多关联（版本管理）

### 2. blog_versions - 文章版本管理
**用途**: 文章的版本历史记录
**记录数**: 34条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `blog_id` | int | 关联文章ID | MUL |
| `version_number` | int | 版本号 | - |
| `title` | varchar(255) | 版本标题 | - |
| `slug` | varchar(255) | 版本标识 | - |
| `description` | text | 版本描述 | - |
| `author` | varchar(100) | 作者 | - |
| `content` | text | 版本内容 | - |
| `display_date` | datetime | 显示日期 | - |
| `published` | tinyint(1) | 是否发布 | - |
| `show_on_homepage` | tinyint(1) | 首页显示 | - |
| `meta_title` | varchar(255) | SEO标题 | - |
| `meta_description` | text | SEO描述 | - |
| `meta_keywords` | text | SEO关键词 | - |
| `og_title` | varchar(255) | OG标题 | - |
| `og_description` | text | OG描述 | - |
| `og_image` | varchar(500) | OG图片 | - |
| `canonical_url` | varchar(500) | 规范URL | - |
| `tags_snapshot` | json | 标签快照 | - |
| `change_summary` | text | 变更摘要 | - |
| `change_type` | varchar(50) | 变更类型 | - |
| `created_by` | varchar(100) | 创建者 | - |
| `created_at` | datetime | 创建时间 | - |
| `content_length` | int | 内容长度 | - |
| `word_count` | int | 字数统计 | - |
| `is_major_change` | tinyint(1) | 是否重大变更 | MUL |

### 3. tags - 统一标签系统
**用途**: 文章、图片等内容的标签管理
**记录数**: 12条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `name` | varchar(100) | 标签名称 | UNI |
| `slug` | varchar(100) | URL标识 | UNI |
| `description` | varchar(500) | 标签描述 | - |
| `color` | varchar(50) | 标签颜色 | - |
| `icon` | varchar(100) | 标签图标 | MUL |
| `category` | varchar(50) | 标签分类 | MUL |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

**关联关系**:
- 与 `blog_tags` 多对多关联（文章标签）
- 与 `image_tags` 多对多关联（图片标签）

### 4. images - 图片管理
**用途**: 图片资源的统一管理
**记录数**: 5条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `url` | varchar(255) | 图片URL | - |
| `thumbnail_url` | varchar(255) | 缩略图URL | - |
| `webp_url` | varchar(255) | WebP格式URL | - |
| `thumbnail_webp_url` | varchar(255) | WebP缩略图URL | - |
| `alt` | varchar(255) | 替代文本 | - |
| `width` | int | 图片宽度 | - |
| `height` | int | 图片高度 | - |
| `date` | date | 拍摄日期 | MUL |
| `location` | varchar(255) | 拍摄地点 | - |
| `caption` | varchar(255) | 图片说明 | - |
| `content` | varchar(255) | 内容描述 | - |
| `storage_type` | varchar(50) | 存储类型 | - |
| `display_name` | varchar(255) | 显示名称 | MUL |
| `description` | text | 详细描述 | - |
| `file_size` | bigint | 文件大小 | - |
| `mime_type` | varchar(100) | MIME类型 | - |
| `original_filename` | varchar(255) | 原始文件名 | - |
| `image_metadata` | json | 图片元数据 | - |
| `view_count` | int | 查看次数 | - |
| `download_count` | int | 下载次数 | - |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

### 5. albums - 相册管理
**用途**: 图片相册的组织管理
**记录数**: 4条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `title` | varchar(255) | 相册标题 | - |
| `description` | text | 相册描述 | - |
| `cover_image_id` | int | 封面图片ID | MUL |
| `date` | date | 相册日期 | - |
| `location` | varchar(255) | 地点 | - |
| `slug` | varchar(255) | URL标识 | MUL |
| `category` | varchar(100) | 相册分类 | MUL |
| `sort_order` | int | 排序顺序 | MUL |
| `is_public` | tinyint(1) | 是否公开 | MUL |
| `is_featured` | tinyint(1) | 是否精选 | MUL |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

### 6. users - 用户系统
**用途**: 系统用户管理
**记录数**: 2条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `username` | varchar(50) | 用户名 | UNI |
| `password_hash` | varchar(128) | 密码哈希 | - |
| `email` | varchar(100) | 邮箱地址 | UNI |
| `role` | varchar(50) | 用户角色 | - |
| `is_active` | tinyint(1) | 是否激活 | - |
| `is_superuser` | tinyint(1) | 是否超级用户 | - |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

### 7. 关联表

#### blog_tags - 文章标签关联
**用途**: 文章与标签的多对多关联
**记录数**: 12条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `blog_id` | int | 文章ID | PRI |
| `tag_id` | int | 标签ID | PRI |

#### album_images - 相册图片关联
**用途**: 相册与图片的多对多关联
**记录数**: 7条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `album_id` | int | 相册ID | PRI |
| `image_id` | int | 图片ID | PRI |
| `display_order` | int | 显示顺序 | - |

#### image_tags - 图片标签关联
**用途**: 图片与标签的多对多关联
**记录数**: 0条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `image_id` | int | 图片ID | PRI |
| `tag_id` | int | 标签ID | PRI |

### 8. about_pages - 关于页面
**用途**: 关于页面内容管理
**记录数**: 1条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `title` | varchar(255) | 页面标题 | - |
| `content` | text | 页面内容 | - |
| `is_active` | tinyint(1) | 是否激活 | - |
| `photo_url` | varchar(500) | 照片URL | - |
| `photo_position` | varchar(50) | 照片位置 | - |
| `photo_style` | varchar(50) | 照片样式 | - |
| `photo_size` | varchar(50) | 照片大小 | - |
| `layout_config` | json | 布局配置 | - |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

## 🔧 配置管理表（4个）

### 1. site_settings - 站点配置
**用途**: 网站基础配置管理
**记录数**: 9条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `setting_key` | varchar(100) | 配置键 | UNI |
| `setting_value` | json | 配置值 | - |
| `setting_type` | enum | 配置类型 | MUL |
| `description` | text | 配置描述 | - |
| `created_at` | timestamp | 创建时间 | - |
| `updated_at` | timestamp | 更新时间 | - |

### 2. seo_settings - SEO配置
**用途**: SEO相关设置管理
**记录数**: 23条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `setting_key` | varchar(100) | 配置键 | UNI |
| `setting_value` | json | 配置值 | - |
| `setting_type` | enum | 配置类型 | MUL |
| `page_type` | varchar(50) | 页面类型 | MUL |
| `content_id` | int | 内容ID | MUL |
| `is_active` | tinyint(1) | 是否激活 | MUL |
| `priority` | int | 优先级 | - |
| `description` | text | 配置描述 | - |
| `created_at` | timestamp | 创建时间 | - |
| `updated_at` | timestamp | 更新时间 | - |

### 3. website_versions - 网站版本
**用途**: 网站版本更新记录
**记录数**: 5条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `version` | varchar(50) | 版本号 | UNI |
| `title` | varchar(255) | 版本标题 | - |
| `content` | text | 版本内容 | - |
| `release_date` | datetime | 发布日期 | - |
| `is_published` | tinyint(1) | 是否发布 | - |
| `is_major` | tinyint(1) | 是否主要版本 | - |
| `author` | varchar(100) | 作者 | - |
| `tags` | varchar(500) | 标签 | - |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

### 4. system_configs - 系统配置
**用途**: 系统级配置（待清理）
**记录数**: 0条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `key` | varchar(100) | 配置键 | UNI |
| `value` | text | 配置值 | - |
| `description` | varchar(255) | 配置描述 | - |
| `config_type` | varchar(50) | 配置类型 | - |
| `is_active` | tinyint(1) | 是否激活 | - |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

## 👤 个人信息表（3个）

### 1. educations - 教育经历
**用途**: 个人教育背景管理
**记录数**: 2条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `school` | varchar(200) | 学校名称 | - |
| `major` | varchar(200) | 专业 | - |
| `degree_type` | enum | 学位类型 | - |
| `start_year` | varchar(10) | 开始年份 | - |
| `end_year` | varchar(10) | 结束年份 | - |
| `logo` | varchar(500) | 学校Logo | - |
| `description` | text | 描述 | - |
| `gpa` | varchar(20) | 成绩 | - |
| `honors` | text | 荣誉 | - |
| `is_current` | tinyint(1) | 是否当前 | - |
| `display_order` | int | 显示顺序 | - |
| `is_visible` | tinyint(1) | 是否可见 | - |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

### 2. careers - 职业经历
**用途**: 个人工作经历管理
**记录数**: 1条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `company` | varchar(200) | 公司名称 | - |
| `position` | varchar(200) | 职位 | - |
| `start_date` | varchar(20) | 开始日期 | - |
| `end_date` | varchar(20) | 结束日期 | - |
| `logo` | varchar(500) | 公司Logo | - |
| `description` | text | 工作描述 | - |
| `location` | varchar(200) | 工作地点 | - |
| `company_url` | varchar(500) | 公司网址 | - |
| `is_current` | tinyint(1) | 是否当前 | - |
| `display_order` | int | 显示顺序 | - |
| `is_visible` | tinyint(1) | 是否可见 | - |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

### 3. navigation_items - 导航配置
**用途**: 网站导航菜单管理
**记录数**: 5条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `name` | varchar(100) | 导航名称 | - |
| `href` | varchar(500) | 链接地址 | - |
| `icon` | varchar(100) | 图标 | - |
| `description` | varchar(500) | 描述 | - |
| `is_external` | tinyint(1) | 是否外部链接 | - |
| `target` | varchar(20) | 打开方式 | - |
| `display_order` | int | 显示顺序 | - |
| `is_visible` | tinyint(1) | 是否可见 | - |
| `is_header` | tinyint(1) | 是否头部显示 | - |
| `is_footer` | tinyint(1) | 是否底部显示 | - |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

## 📝 内容管理表（3个）

### 1. content_templates - 内容模板
**用途**: 文章和内容的模板管理
**记录数**: 23条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `name` | varchar(255) | 模板名称 | - |
| `description` | text | 模板描述 | - |
| `title_template` | text | 标题模板 | - |
| `content_template` | text | 内容模板 | - |
| `meta_template` | json | 元数据模板 | - |
| `fields_config` | json | 字段配置 | - |
| `validation_rules` | json | 验证规则 | - |
| `tags` | json | 标签 | - |
| `template_type` | varchar(50) | 模板类型 | - |
| `is_active` | tinyint(1) | 是否激活 | MUL |
| `is_default` | tinyint(1) | 是否默认 | - |
| `is_system` | tinyint(1) | 是否系统模板 | - |
| `usage_count` | int | 使用次数 | - |
| `created_by` | int | 创建者ID | MUL |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

### 2. content_drafts - 草稿系统
**用途**: 内容草稿的临时存储
**记录数**: 0条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `title` | varchar(500) | 草稿标题 | - |
| `content_type` | varchar(50) | 内容类型 | MUL |
| `content_id` | int | 关联内容ID | - |
| `draft_data` | json | 草稿数据 | - |
| `template_id` | int | 模板ID | MUL |
| `status` | varchar(20) | 草稿状态 | MUL |
| `version` | int | 版本号 | - |
| `parent_draft_id` | int | 父草稿ID | MUL |
| `auto_save_interval` | int | 自动保存间隔 | - |
| `last_auto_save` | datetime | 最后自动保存时间 | - |
| `created_by` | int | 创建者ID | MUL |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

### 3. template_categories - 模板分类
**用途**: 内容模板的分类管理
**记录数**: 5条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `name` | varchar(100) | 分类名称 | UNI |
| `display_name` | varchar(100) | 显示名称 | - |
| `description` | text | 分类描述 | - |
| `icon` | varchar(50) | 分类图标 | - |
| `color` | varchar(20) | 分类颜色 | - |
| `sort_order` | int | 排序顺序 | - |
| `is_active` | tinyint(1) | 是否激活 | - |
| `is_system` | tinyint(1) | 是否系统分类 | - |
| `is_universal` | tinyint(1) | 是否通用 | - |
| `created_by` | int | 创建者ID | MUL |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

#### template_category_associations - 模板分类关联
**用途**: 模板与分类的多对多关联
**记录数**: 0条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `template_id` | int | 模板ID | PRI |
| `category_id` | int | 分类ID | PRI |

## 🎨 图标系统表（7个）

### 1. icon_libraries - 图标库
**用途**: 图标库的基础信息管理
**记录数**: 3条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `name` | varchar(50) | 图标库名称 | UNI |
| `display_name` | varchar(100) | 显示名称 | - |
| `description` | text | 图标库描述 | - |
| `base_url` | varchar(255) | 基础URL | - |
| `version` | varchar(20) | 版本号 | - |
| `sort_order` | int | 排序顺序 | - |
| `config` | json | 配置信息 | - |
| `created_at` | timestamp | 创建时间 | - |
| `updated_at` | timestamp | 更新时间 | - |

### 2. icon_categories - 图标分类
**用途**: 图标的分类管理
**记录数**: 19条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `library_id` | int | 图标库ID | MUL |
| `name` | varchar(100) | 分类名称 | - |
| `display_name` | varchar(100) | 显示名称 | - |
| `description` | text | 分类描述 | - |
| `color` | varchar(7) | 分类颜色 | - |
| `icon` | varchar(100) | 分类图标 | - |
| `parent_id` | int | 父分类ID | MUL |
| `sort_order` | int | 排序顺序 | - |
| `created_at` | timestamp | 创建时间 | - |
| `updated_at` | timestamp | 更新时间 | - |

### 3. icon_metadata - 图标元数据
**用途**: 图标的详细信息和元数据
**记录数**: 1814条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `library_id` | int | 图标库ID | MUL |
| `category_id` | int | 分类ID | MUL |
| `icon_key` | varchar(200) | 图标键名 | - |
| `display_name` | varchar(200) | 显示名称 | - |
| `description` | text | 图标描述 | - |
| `tags` | json | 标签 | - |
| `aliases` | json | 别名 | - |
| `has_dark_variant` | tinyint(1) | 是否有深色变体 | - |
| `has_light_variant` | tinyint(1) | 是否有浅色变体 | - |
| `default_size` | int | 默认大小 | - |
| `min_size` | int | 最小大小 | - |
| `max_size` | int | 最大大小 | - |
| `usage_count` | int | 使用次数 | MUL |
| `is_favorite` | tinyint(1) | 是否收藏 | - |
| `cached_svg_light` | text | 缓存的浅色SVG | - |
| `cached_svg_dark` | text | 缓存的深色SVG | - |
| `cache_expires_at` | timestamp | 缓存过期时间 | - |
| `last_used_at` | timestamp | 最后使用时间 | MUL |
| `created_at` | timestamp | 创建时间 | - |
| `updated_at` | timestamp | 更新时间 | - |

### 4. icon_usage - 图标使用记录
**用途**: 图标使用情况的统计记录
**记录数**: 76848条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `icon_id` | int | 图标ID | MUL |
| `usage_context` | varchar(100) | 使用上下文 | MUL |
| `context_id` | varchar(100) | 上下文ID | - |
| `usage_type` | varchar(50) | 使用类型 | - |
| `theme` | varchar(10) | 主题 | - |
| `size` | int | 使用大小 | - |
| `user_id` | int | 用户ID | - |
| `ip_address` | varchar(45) | IP地址 | - |
| `user_agent` | varchar(500) | 用户代理 | - |
| `created_at` | timestamp | 创建时间 | MUL |

### 5. icon_favorites - 图标收藏
**用途**: 用户图标收藏功能
**记录数**: 0条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `icon_id` | int | 图标ID | MUL |
| `user_id` | int | 用户ID | - |
| `session_id` | varchar(100) | 会话ID | - |
| `created_at` | timestamp | 创建时间 | - |

### 6. icon_favorite_folders - 图标收藏夹
**用途**: 图标收藏的文件夹管理
**记录数**: 3条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `name` | varchar(100) | 文件夹名称 | - |
| `display_name` | varchar(200) | 显示名称 | - |
| `description` | text | 文件夹描述 | - |
| `color` | varchar(7) | 文件夹颜色 | - |
| `icon` | varchar(50) | 文件夹图标 | - |
| `sort_order` | int | 排序顺序 | MUL |
| `is_default` | tinyint(1) | 是否默认 | MUL |
| `created_at` | timestamp | 创建时间 | - |
| `updated_at` | timestamp | 更新时间 | - |

### 7. icon_favorite_items - 图标收藏项
**用途**: 收藏夹中的具体图标项
**记录数**: 6条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `favorite_id` | int | 收藏夹ID | MUL |
| `icon_id` | int | 图标ID | MUL |
| `sort_order` | int | 排序顺序 | MUL |
| `notes` | text | 备注 | - |
| `created_at` | timestamp | 创建时间 | - |

## 📊 系统监控表（2个）

### 1. system_metrics_history - 系统指标历史
**用途**: 系统性能指标的历史记录
**记录数**: 2031条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `timestamp` | datetime | 时间戳 | MUL |
| `cpu_usage` | float | CPU使用率 | - |
| `cpu_count` | int | CPU核心数 | - |
| `cpu_freq` | json | CPU频率信息 | - |
| `memory_usage` | float | 内存使用率 | - |
| `memory_total` | bigint | 总内存 | - |
| `memory_used` | bigint | 已用内存 | - |
| `memory_available` | bigint | 可用内存 | - |
| `disk_usage` | float | 磁盘使用率 | - |
| `disk_total` | bigint | 总磁盘空间 | - |
| `disk_used` | bigint | 已用磁盘空间 | - |
| `disk_free` | bigint | 可用磁盘空间 | - |
| `network_io` | json | 网络IO信息 | - |
| `active_connections` | int | 活跃连接数 | - |
| `response_time` | float | 响应时间 | - |
| `error_rate` | float | 错误率 | - |
| `uptime` | int | 运行时间 | - |
| `system_info` | json | 系统信息 | - |

### 2. api_metrics - API指标
**用途**: API调用的统计和监控
**记录数**: 12734条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `timestamp` | datetime | 时间戳 | MUL |
| `endpoint` | text | API端点 | - |
| `method` | text | HTTP方法 | - |
| `status_code` | int | 状态码 | - |
| `response_time` | float | 响应时间 | - |
| `user_id` | int | 用户ID | - |
| `ip_address` | text | IP地址 | - |
| `user_agent` | text | 用户代理 | - |

## 📅 时间线系统表（2个）

### 1. gallery_timeline_entries - 时间线条目
**用途**: 画廊时间线的条目管理
**记录数**: 9条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `title` | varchar(200) | 条目标题 | - |
| `content` | text | 条目内容 | - |
| `location` | varchar(200) | 地点 | - |
| `slug` | varchar(100) | URL标识 | UNI |
| `is_active` | tinyint(1) | 是否激活 | - |
| `is_featured` | tinyint(1) | 是否精选 | - |
| `show_on_homepage` | tinyint(1) | 首页显示 | - |
| `view_count` | int | 查看次数 | - |
| `like_count` | int | 点赞次数 | - |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

### 2. gallery_timeline_images - 时间线图片关联
**用途**: 时间线条目与图片的关联
**记录数**: 8条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `timeline_entry_id` | int | 时间线条目ID | PRI |
| `image_id` | int | 图片ID | PRI |
| `sort_order` | int | 排序顺序 | - |
| `caption` | varchar(500) | 图片说明 | - |

## 🎨 布局主题表（6个）

### 1. themes - 主题管理
**用途**: 网站主题的配置管理
**记录数**: 6条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `name` | varchar(255) | 主题名称 | UNI |
| `display_name` | varchar(255) | 显示名称 | - |
| `description` | text | 主题描述 | - |
| `preview_image` | varchar(500) | 预览图片 | - |
| `theme_type` | varchar(50) | 主题类型 | MUL |
| `colors` | json | 颜色配置 | - |
| `fonts` | json | 字体配置 | - |
| `animations` | json | 动画配置 | - |
| `layout_settings` | json | 布局设置 | - |
| `custom_css` | text | 自定义CSS | - |
| `css_variables` | json | CSS变量 | - |
| `is_active` | tinyint(1) | 是否激活 | MUL |
| `is_default` | tinyint(1) | 是否默认 | - |
| `is_public` | tinyint(1) | 是否公开 | - |
| `version` | varchar(20) | 版本号 | - |
| `author` | varchar(255) | 作者 | - |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

### 2. theme_presets - 主题预设
**用途**: 预定义的主题配置
**记录数**: 9条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `name` | varchar(255) | 预设名称 | UNI |
| `display_name` | varchar(255) | 显示名称 | - |
| `description` | text | 预设描述 | - |
| `category` | varchar(100) | 预设分类 | MUL |
| `preset_config` | json | 预设配置 | - |
| `preview_image` | varchar(500) | 预览图片 | - |
| `preview_colors` | json | 预览颜色 | - |
| `tags` | json | 标签 | - |
| `is_featured` | tinyint(1) | 是否精选 | MUL |
| `is_active` | tinyint(1) | 是否激活 | - |
| `usage_count` | int | 使用次数 | - |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

### 3. page_layouts - 页面布局
**用途**: 不同页面类型的布局配置
**记录数**: 4条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `page_type` | varchar(50) | 页面类型 | UNI |
| `layout_name` | varchar(255) | 布局名称 | - |
| `description` | text | 布局描述 | - |
| `layout_config` | json | 布局配置 | - |
| `block_order` | json | 块顺序 | - |
| `desktop_layout` | json | 桌面布局 | - |
| `tablet_layout` | json | 平板布局 | - |
| `mobile_layout` | json | 移动端布局 | - |
| `is_active` | tinyint(1) | 是否激活 | MUL |
| `version` | varchar(20) | 版本号 | - |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

### 4. layout_blocks - 布局块
**用途**: 页面布局的组成块管理
**记录数**: 10条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `block_id` | varchar(100) | 块ID | UNI |
| `name` | varchar(255) | 块名称 | - |
| `description` | text | 块描述 | - |
| `enabled` | tinyint(1) | 是否启用 | - |
| `display_order` | int | 显示顺序 | MUL |
| `page_type` | varchar(50) | 页面类型 | MUL |
| `config` | json | 配置信息 | - |
| `desktop_config` | json | 桌面配置 | - |
| `mobile_config` | json | 移动端配置 | - |
| `css_classes` | text | CSS类 | - |
| `inline_styles` | text | 内联样式 | - |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

## 🔧 其他表（4个）

### 1. image_categories - 图片分类（待清理）
**用途**: 图片分类管理（已被标签系统替代）
**记录数**: 0条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `id` | int | 主键ID | PRI |
| `name` | varchar(100) | 分类名称 | - |
| `slug` | varchar(100) | URL标识 | UNI |
| `description` | text | 分类描述 | - |
| `parent_id` | int | 父分类ID | MUL |
| `color` | varchar(7) | 分类颜色 | - |
| `icon` | varchar(50) | 分类图标 | - |
| `cover_image_id` | int | 封面图片ID | MUL |
| `sort_order` | int | 排序顺序 | - |
| `is_system` | tinyint(1) | 是否系统分类 | - |
| `is_active` | tinyint(1) | 是否激活 | - |
| `image_count` | int | 图片数量 | - |
| `created_at` | datetime | 创建时间 | - |
| `updated_at` | datetime | 更新时间 | - |

### 2. alembic_version - Alembic版本
**用途**: 数据库迁移版本管理
**记录数**: 1条

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| `version_num` | varchar(32) | 版本号 | PRI |

## 📈 数据库关系图

### 核心关系
```
users (1) ←→ (N) blogs
blogs (N) ←→ (N) tags [through blog_tags]
images (N) ←→ (N) tags [through image_tags]
albums (1) ←→ (N) album_images ←→ (N) images
gallery_timeline_entries (1) ←→ (N) gallery_timeline_images ←→ (N) images
```

### 配置关系
```
content_templates (N) ←→ (N) template_categories [through template_category_associations]
icon_libraries (1) ←→ (N) icon_categories (1) ←→ (N) icon_metadata
icon_favorite_folders (1) ←→ (N) icon_favorite_items ←→ (N) icon_metadata
```

## 🔍 索引优化建议

### 高频查询索引
- `blogs`: `(article_type, published)`, `(display_date, published)`, `(featured, published)`
- `images`: `(date, created_at)`
- `tags`: `(category, name)`
- `albums`: `(is_public, is_featured)`
- `website_versions`: `(published_date)`

### 外键索引
- 所有外键字段都应有对应索引
- 多对多关联表的复合主键自动创建索引

## 📝 维护建议

### 定期清理
1. **api_metrics**: 定期清理旧的API调用记录
2. **system_metrics_history**: 定期清理旧的系统指标
3. **icon_usage**: 定期归档使用记录
4. **image_categories**: 完全删除（已被标签系统替代）
5. **system_configs**: 迁移到site_settings后删除

### 性能优化
1. 为高频查询字段添加索引
2. 定期分析慢查询日志
3. 考虑对大表进行分区
4. 优化JSON字段的查询方式

### 数据完整性
1. 定期检查外键约束
2. 验证关联表的数据一致性
3. 监控孤立记录
4. 定期备份重要数据

---

**文档创建时间**: 2025-08-05 15:25
**文档版本**: v1.0
**最后更新**: 2025-08-05 15:30
**维护者**: 开发团队
**下次审核**: 2025-09-05
