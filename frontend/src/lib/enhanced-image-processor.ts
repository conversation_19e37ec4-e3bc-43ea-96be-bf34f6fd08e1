/**
 * 增强图片处理器
 * 支持扩展的图片Markdown语法
 */

export interface ImageOptions {
  size?: 'small' | 'medium' | 'large' | 'full';
  align?: 'left' | 'center' | 'right' | 'float-left' | 'float-right';
  caption?: string;
  alt?: string;
  title?: string;
  width?: number;
  height?: number;
}

/**
 * 处理增强的图片语法
 * 支持多种格式：
 * 1. 标准Markdown: ![alt](url)
 * 2. 带尺寸: ![alt](url){size=medium}
 * 3. 带对齐: ![alt](url){align=center}
 * 4. 带说明: ![alt](url "title"){caption=说明文字}
 * 5. 组合属性: ![alt](url "title"){size=large,align=center,caption=说明}
 */
export function processEnhancedImages(html: string): string {
  // 处理增强语法的图片
  const enhancedImageRegex = /!\[([^\]]*)\]\(([^)]+)(?:\s+"([^"]*)")?\)\{([^}]+)\}/g;
  
  html = html.replace(enhancedImageRegex, (match, alt, src, title, options) => {
    const imageOptions = parseImageOptions(options);
    return generateImageHTML(src, alt, title, imageOptions);
  });

  // 处理带title的标准图片
  const titleImageRegex = /!\[([^\]]*)\]\(([^)]+)\s+"([^"]*)"\)/g;
  html = html.replace(titleImageRegex, (match, alt, src, title) => {
    return generateImageHTML(src, alt, title, { size: 'medium', align: 'center' });
  });

  return html;
}

/**
 * 解析图片选项字符串
 * 例如: "size=large,align=center,caption=这是说明"
 */
function parseImageOptions(optionsStr: string): ImageOptions {
  const options: ImageOptions = {};
  
  // 分割选项，支持逗号分隔
  const pairs = optionsStr.split(',').map(pair => pair.trim());
  
  for (const pair of pairs) {
    const [key, value] = pair.split('=').map(s => s.trim());
    
    switch (key) {
      case 'size':
        if (['small', 'medium', 'large', 'full'].includes(value)) {
          options.size = value as any;
        }
        break;
      case 'align':
        if (['left', 'center', 'right', 'float-left', 'float-right'].includes(value)) {
          options.align = value as any;
        }
        break;
      case 'caption':
        options.caption = value;
        break;
      case 'width':
        const width = parseInt(value);
        if (!isNaN(width)) options.width = width;
        break;
      case 'height':
        const height = parseInt(value);
        if (!isNaN(height)) options.height = height;
        break;
    }
  }
  
  return options;
}

/**
 * 生成图片HTML
 */
function generateImageHTML(src: string, alt: string, title?: string, options: ImageOptions = {}): string {
  const {
    size = 'medium',
    align = 'center',
    caption,
    width,
    height
  } = options;

  // 构建img标签属性
  let imgAttrs = `src="${src}" alt="${alt}"`;
  if (title) imgAttrs += ` title="${title}"`;
  if (width) imgAttrs += ` width="${width}"`;
  if (height) imgAttrs += ` height="${height}"`;
  
  // 添加CSS类
  imgAttrs += ` class="image-${size}"`;

  // 构建容器
  let html = '';
  
  if (align === 'float-left' || align === 'float-right') {
    // 浮动布局
    const floatClass = align.replace('float-', '');
    html = `<div class="image-float image-float-${floatClass}">`;
    html += `<img ${imgAttrs} />`;
    if (caption) {
      html += `<p class="image-caption">${caption}</p>`;
    }
    html += `</div>`;
  } else {
    // 标准布局
    html = `<div class="image-container image-${align}">`;
    html += `<img ${imgAttrs} />`;
    if (caption) {
      html += `<p class="image-caption">${caption}</p>`;
    }
    html += `</div>`;
  }

  return html;
}

/**
 * 处理图片组合/画廊语法
 * 语法: [gallery:2] ![](url1) ![](url2) [/gallery]
 */
export function processImageGalleries(html: string): string {
  const galleryRegex = /\[gallery:(\d+)\]([\s\S]*?)\[\/gallery\]/g;
  
  return html.replace(galleryRegex, (match, columns, content) => {
    const cols = parseInt(columns) || 2;
    
    // 提取图片
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)(?:\s+"([^"]*)")?\)/g;
    const images: Array<{alt: string, src: string, title?: string}> = [];
    
    let imageMatch;
    while ((imageMatch = imageRegex.exec(content)) !== null) {
      images.push({
        alt: imageMatch[1],
        src: imageMatch[2],
        title: imageMatch[3]
      });
    }
    
    if (images.length === 0) return match;
    
    // 生成画廊HTML
    let galleryHtml = `<div class="image-gallery gallery-${cols}">`;
    
    for (const img of images) {
      galleryHtml += `<div class="gallery-item">`;
      galleryHtml += `<img src="${img.src}" alt="${img.alt}"${img.title ? ` title="${img.title}"` : ''} class="image-medium" />`;
      galleryHtml += `</div>`;
    }
    
    galleryHtml += `</div>`;
    
    return galleryHtml;
  });
}

/**
 * 处理图片对比语法
 * 语法: [compare] ![](before.jpg) ![](after.jpg) [/compare]
 */
export function processImageComparisons(html: string): string {
  const compareRegex = /\[compare\]([\s\S]*?)\[\/compare\]/g;
  
  return html.replace(compareRegex, (match, content) => {
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)(?:\s+"([^"]*)")?\)/g;
    const images: Array<{alt: string, src: string, title?: string}> = [];
    
    let imageMatch;
    while ((imageMatch = imageRegex.exec(content)) !== null) {
      images.push({
        alt: imageMatch[1],
        src: imageMatch[2],
        title: imageMatch[3]
      });
    }
    
    if (images.length !== 2) return match;
    
    // 生成对比HTML
    let compareHtml = `<div class="image-comparison">`;
    compareHtml += `<div class="comparison-before">`;
    compareHtml += `<img src="${images[0].src}" alt="${images[0].alt}" class="image-medium" />`;
    compareHtml += `<span class="comparison-label">Before</span>`;
    compareHtml += `</div>`;
    compareHtml += `<div class="comparison-after">`;
    compareHtml += `<img src="${images[1].src}" alt="${images[1].alt}" class="image-medium" />`;
    compareHtml += `<span class="comparison-label">After</span>`;
    compareHtml += `</div>`;
    compareHtml += `</div>`;
    
    return compareHtml;
  });
}

/**
 * 主处理函数
 */
export function processAllEnhancedImages(html: string): string {
  // 按顺序处理各种图片语法
  html = processImageGalleries(html);
  html = processImageComparisons(html);
  html = processEnhancedImages(html);
  
  return html;
}
