@tailwind base;
@tailwind components;
@import './prism.css';
@import './red-dot-animations.css';
@import './gallery-animations.css';
@import './waline/waline-simple.css';
@tailwind utilities;

/* 增强图片样式 */
@layer components {
  /* 图片容器基础样式 */
  .image-container {
    @apply my-6 flex flex-col clear-both;
  }

  /* 图片对齐方式 */
  .image-container.image-left {
    @apply items-start;
  }

  .image-container.image-center {
    @apply items-center;
  }

  .image-container.image-right {
    @apply items-end;
  }

  /* 浮动图片容器 */
  .image-float {
    @apply m-4 max-w-[50%] inline-block;
  }

  .image-float.image-float-left {
    @apply float-left mr-6 ml-0;
  }

  .image-float.image-float-right {
    @apply float-right ml-6 mr-0;
  }

  /* 图片尺寸样式 */
  .image-small {
    @apply max-w-[300px] w-full h-auto rounded-lg shadow-md transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg cursor-pointer;
  }

  .image-medium {
    @apply max-w-[600px] w-full h-auto rounded-lg shadow-md transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg cursor-pointer;
  }

  .image-large {
    @apply max-w-[900px] w-full h-auto rounded-lg shadow-md transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg cursor-pointer;
  }

  .image-full {
    @apply w-full h-auto rounded-lg shadow-md transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg cursor-pointer;
  }

  /* 图片说明文字 */
  .image-caption {
    @apply mt-2 mb-0 text-sm text-gray-600 dark:text-gray-400 text-center italic leading-relaxed;
  }

  .image-float .image-caption {
    @apply text-left text-xs;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .image-float {
      @apply float-none max-w-full mx-0 my-4 block;
    }

    .image-large,
    .image-medium {
      @apply max-w-full;
    }

    .image-small {
      @apply max-w-[250px];
    }
  }

  @media (max-width: 480px) {
    .image-small {
      @apply max-w-[200px];
    }

    .image-caption {
      @apply text-xs;
    }
  }

  /* 图片画廊样式 */
  .image-gallery {
    @apply grid gap-4 my-6;
  }

  .image-gallery.gallery-2 {
    @apply grid-cols-2;
  }

  .image-gallery.gallery-3 {
    @apply grid-cols-3;
  }

  .image-gallery.gallery-4 {
    @apply grid-cols-4;
  }

  .gallery-item {
    @apply overflow-hidden rounded-lg;
  }

  .gallery-item img {
    @apply w-full h-full object-cover transition-transform duration-300 hover:scale-105;
  }

  @media (max-width: 768px) {
    .image-gallery.gallery-3,
    .image-gallery.gallery-4 {
      @apply grid-cols-2;
    }
  }

  @media (max-width: 480px) {
    .image-gallery.gallery-2,
    .image-gallery.gallery-3,
    .image-gallery.gallery-4 {
      @apply grid-cols-1;
    }
  }

  /* 图片对比样式 */
  .image-comparison {
    @apply flex gap-4 my-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg;
  }

  .comparison-before,
  .comparison-after {
    @apply flex-1 relative;
  }

  .comparison-label {
    @apply absolute top-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm font-medium;
  }

  @media (max-width: 768px) {
    .image-comparison {
      @apply flex-col gap-2;
    }
  }
}

/* 霞鹜文楷字体定义 */
@font-face {
  font-family: 'LXGW WenKai';
  src: url('/fonts/lxgw-wenkai/LXGWWenKai-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'LXGW WenKai Light';
  src: url('/fonts/lxgw-wenkai/LXGWWenKai-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'LXGW WenKai Medium';
  src: url('/fonts/lxgw-wenkai/LXGWWenKai-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* 自定义动画效果 */
@layer utilities {
  /* 主页时间线动画 */
  .animate-fade-in-up {
    animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }

  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  /* 高级悬停动画 */
  .timeline-card-hover {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .timeline-card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    filter: drop-shadow(0 25px 50px rgba(0, 0, 0, 0.15));
  }

  /* 渐变边框动画 */
  .gradient-border {
    position: relative;
    background: linear-gradient(45deg, transparent, transparent);
    border-radius: 1rem;
  }

  .gradient-border::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 1px;
    background: linear-gradient(45deg, hsl(var(--primary)), hsl(var(--secondary)));
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .gradient-border:hover::before {
    opacity: 1;
  }

  /* 文本截断 */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 脉冲动画 */
  .animate-pulse-soft {
    animation: pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulseSoft {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  /* 光晕效果 */
  .glow-effect {
    position: relative;
  }

  .glow-effect::after {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, hsl(var(--primary)), hsl(var(--secondary)));
    border-radius: inherit;
    opacity: 0;
    filter: blur(10px);
    z-index: -1;
    transition: opacity 0.3s ease;
  }

  .glow-effect:hover::after {
    opacity: 0.3;
  }

  /* 目录滚动定位优化 */
  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6,
  h1, h2, h3, h4, h5, h6 {
    scroll-margin-top: 180px;
  }

  /* 确保页面末尾有足够的空间 - 修复：使用绝对定位避免占据文档流空间 */
  .prose::after,
  [data-mdx-content]::after {
    content: '';
    position: absolute;
    bottom: -50vh;
    left: 0;
    right: 0;
    height: 50vh;
    pointer-events: none;
    z-index: -1;
  }

  /* 为使用::after的容器添加相对定位 */
  .prose,
  [data-mdx-content] {
    position: relative;
  }

  /* 文章末尾兜底：禁用最后几个元素的::after占位，避免尾部空白 */
  .article-content .prose > *:is(:last-child, :nth-last-child(2), :nth-last-child(3))::after,
  [data-mdx-content] > *:is(:last-child, :nth-last-child(2), :nth-last-child(3))::after {
    content: none !important;
    display: none !important;
  }

  /* 防止prose内容被分列显示 */
  .prose,
  .prose-zinc,
  .prose-invert,
  [class*="prose"] {
    column-count: 1 !important;
    column-fill: auto !important;
    column-gap: 0 !important;
    column-rule: none !important;
    column-span: none !important;
    column-width: auto !important;
    columns: 1 !important;
    -webkit-column-count: 1 !important;
    -moz-column-count: 1 !important;
    -webkit-columns: 1 !important;
    -moz-columns: 1 !important;
  }

  /* 确保prose内容中的段落、列表项等不被分割 */
  .prose p,
  .prose li,
  .prose blockquote,
  .prose div {
    break-inside: avoid !important;
    page-break-inside: avoid !important;
    column-break-inside: avoid !important;
  }

  /* 特别处理强调文本和内联元素，确保不被分割 */
  .prose strong,
  .prose em,
  .prose b,
  .prose i,
  .prose a,
  .prose code,
  .prose span {
    display: inline !important;
    break-inside: avoid !important;
    page-break-inside: avoid !important;
    column-break-inside: avoid !important;
  }

  /* 确保列表项内容完整显示 */
  .prose li {
    display: block !important;
    break-inside: avoid !important;
    page-break-inside: avoid !important;
    column-break-inside: avoid !important;
    white-space: normal !important;
  }

  /* 强制所有prose内容使用单列布局 */
  .prose *,
  .prose *::before,
  .prose *::after {
    column-span: none !important;
    column-count: 1 !important;
    columns: 1 !important;
    -webkit-column-count: 1 !important;
    -moz-column-count: 1 !important;
  }

  /* 全局禁用多列布局 */
  .rich-text-content,
  .rich-text-content *,
  [data-mdx-content],
  [data-mdx-content] * {
    column-count: 1 !important;
    columns: 1 !important;
    column-fill: auto !important;
    column-gap: 0 !important;
    column-rule: none !important;
    column-span: none !important;
    column-width: auto !important;
    -webkit-column-count: 1 !important;
    -moz-column-count: 1 !important;
    -webkit-columns: 1 !important;
    -moz-columns: 1 !important;
    break-inside: avoid !important;
    page-break-inside: avoid !important;
    column-break-inside: avoid !important;
  }

  /* 确保prose内容不受masonry和grid影响 */
  .prose.masonry-grid,
  .prose .masonry-grid,
  .prose.masonry-item,
  .prose .masonry-item,
  .prose.album-masonry,
  .prose .album-masonry,
  .prose.album-masonry-item,
  .prose .album-masonry-item {
    column-count: 1 !important;
    columns: 1 !important;
    display: block !important;
  }

  /* 重置所有可能的多列布局 */
  body .prose,
  body .prose *,
  body .rich-text-content,
  body .rich-text-content *,
  body [data-mdx-content],
  body [data-mdx-content] * {
    column-count: 1 !important;
    columns: 1 !important;
    -webkit-column-count: 1 !important;
    -moz-column-count: 1 !important;
  }
  /* 标签悬停效果 */
  .tag-hover-effect {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .tag-hover-effect:hover {
    background-color: var(--hover-bg);
    color: var(--hover-color);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
  }

  /* 卡片悬停效果 */
  .card-hover-enhanced {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-hover-enhanced:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.15);
  }

  /* 项目网格容器 - 确保hover效果不被裁剪 */
  .project-grid-container {
    overflow: visible;
    padding: 1rem;
  }

  /* 项目卡片hover优化 */
  .project-card-hover {
    transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
  }

  .project-card-hover:hover {
    transform: translateY(-4px) scale(1.015);
    z-index: 10;
  }

  /* 渐变文字效果 */
  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.7) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* 玻璃态效果 */
  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* 暗色模式玻璃态 */
  .dark .glass-effect {
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* 博客时间线动画 */
  .timeline-node {
    animation: timeline-pulse 2s ease-in-out infinite;
  }

  @keyframes timeline-pulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0.4);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 0 8px rgba(var(--primary-rgb), 0);
    }
  }

  /* 增强的悬停效果 */
  .enhanced-hover {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .enhanced-hover:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
      0 20px 40px -12px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(var(--primary-rgb), 0.1);
  }

  /* 闪烁效果 */
  .shimmer {
    position: relative;
    overflow: hidden;
  }

  .shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.7s ease;
  }

  .shimmer:hover::before {
    left: 100%;
  }

  /* 脉冲动画 */
  .pulse-soft {
    animation: pulse-soft 3s ease-in-out infinite;
  }

  @keyframes pulse-soft {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.6;
    }
  }

  /* 发光效果 */
  .glow-pulse {
    animation: glow-pulse 4s ease-in-out infinite;
  }

  @keyframes glow-pulse {
    0%, 100% {
      opacity: 0.5;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.1);
    }
  }

  /* 渐变径向背景 */
  .bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }

  /* 增强的边框效果 */
  .border-3 {
    border-width: 3px;
  }

  /* 动画延迟工具类 */
  .animate-delay-1 {
    animation-delay: 1s;
  }

  .animate-delay-2 {
    animation-delay: 2s;
  }

  .animate-delay-3 {
    animation-delay: 3s;
  }

  .animate-delay-4 {
    animation-delay: 4s;
  }

  /* 自定义阴影 */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(var(--primary-rgb), 0.3);
  }

  .shadow-glow-lg {
    box-shadow: 0 0 40px rgba(var(--primary-rgb), 0.4);
  }

  /* 文字渐变动画 */
  .text-gradient-animate {
    background: linear-gradient(
      45deg,
      hsl(var(--primary)),
      hsl(var(--primary) / 0.8),
      hsl(var(--secondary)),
      hsl(var(--primary))
    );
    background-size: 300% 300%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-shift 3s ease-in-out infinite;
  }

  @keyframes gradient-shift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  /* 平滑滚动 */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* 自定义滚动条 */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }
}
@layer base {
  :root {
      --background: 0 0% 100%;
      --foreground: 240 10% 3.9%;
      --card: 0 0% 100%;        /* 浅灰色背景 */
      --card-foreground: 240 10% 3.9%;  /* 保持原来的深色文字 */
      --popover: 0 0% 100%;
      --popover-foreground: 240 10% 3.9%;
      --primary: 171 70% 35%;      /* 稍微加深主色提高对比度 */
      --primary-foreground: 0 0% 100%;
      --secondary: 240 4.8% 95.9%;
      --secondary-foreground: 240 5.9% 10%;
      --muted: 240 4.8% 95.9%;
      --muted-foreground: 240 3.8% 46.1%;
      --accent: 240 4.8% 95.9%;
      --accent-foreground: 240 5.9% 10%;
      --destructive: 0 84.2% 60.2%;
      --destructive-foreground: 0 0% 98%;
      --border: 240 5.9% 90%;
      --input: 240 5.9% 90%;
      --ring: 171 70% 35%;  /* 使用主色作为焦点环 */

      /* 新增状态色彩 */
      --success: 142 76% 36%;
      --success-foreground: 0 0% 98%;
      --warning: 38 92% 50%;
      --warning-foreground: 0 0% 98%;
      --info: 221 83% 53%;
      --info-foreground: 0 0% 98%;
      --radius: 0.75rem;
      --chart-1: 12 76% 61%;
      --chart-2: 173 58% 39%;
      --chart-3: 197 37% 24%;
      --chart-4: 43 74% 66%;
      --chart-5: 27 87% 67%;
    }

    .dark {
      /* 暗色模式基础色彩 - 增强对比度 */
      --background: 240 10% 3.9%;
      --foreground: 0 0% 98%;
      --card: 240 10% 8%;        /* 稍微提亮卡片背景 */
      --card-foreground: 0 0% 98%;
      --popover: 240 10% 3.9%;
      --popover-foreground: 0 0% 98%;

      /* 暗色模式主色调 - 提高亮度 */
      --primary: 171 70% 55%;      /* 提高主色亮度 */
      --primary-foreground: 240 10% 3.9%;
      --secondary: 240 3.7% 15.9%;
      --secondary-foreground: 0 0% 98%;

      /* 暗色模式辅助色彩 */
      --muted: 240 3.7% 15.9%;
      --muted-foreground: 240 5% 64.9%;
      --accent: 240 3.7% 15.9%;
      --accent-foreground: 0 0% 98%;
      --destructive: 0 62.8% 30.6%;
      --destructive-foreground: 0 0% 98%;

      /* 暗色模式边框和输入 */
      --border: 240 3.7% 15.9%;
      --input: 240 3.7% 15.9%;
      --ring: 171 70% 55%;  /* 使用主色作为焦点环 */

      /* 暗色模式状态色彩 */
      --success: 142 76% 45%;
      --success-foreground: 240 10% 3.9%;
      --warning: 38 92% 60%;
      --warning-foreground: 240 10% 3.9%;
      --info: 221 83% 63%;
      --info-foreground: 240 10% 3.9%;
      --ring: 240 4.9% 83.9%;
      --chart-1: 220 70% 50%;
      --chart-2: 160 60% 45%;
      --chart-3: 30 80% 55%;
      --chart-4: 280 65% 60%;
      --chart-5: 340 75% 55%;
    }
  }
  
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-wenkai;
  }
}

/* Gallery增强动画 */
@layer utilities {
  .perspective-1000 {
    perspective: 1000px;
  }

  .preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-glow-pulse {
    animation: glowPulse 2s ease-in-out infinite;
  }

  .animate-breathing-soft {
    animation: breathingSoft 4s ease-in-out infinite;
  }

  /* Timeline优化 - 防止悬停溢出 */
  .timeline-container {
    overflow: visible;
    padding: 1rem;
  }

  .timeline-card {
    transform-origin: center center;
    will-change: transform, box-shadow;
  }

  .timeline-card:hover {
    z-index: 10;
  }

  /* 安全的悬停变换 */
  .safe-hover-transform {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center center;
  }

  .safe-hover-transform:hover {
    transform: translateY(-4px) scale(1.01);
    z-index: 10;
  }

  /* 增强的缩略图导航栏样式 */
  .thumbnail-collapse {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .thumbnail-expand {
    max-height: 200px;
    opacity: 1;
    transform: translateY(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* 缩略图滚动条美化 - 适配新的深色背景 */
  .thumbnail-scroll {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
  }

  .thumbnail-scroll::-webkit-scrollbar {
    height: 6px;
  }

  .thumbnail-scroll::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  .thumbnail-scroll::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    transition: background 0.2s ease;
  }

  .thumbnail-scroll::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.4);
  }

  /* 缩略图按钮悬停效果 */
  .thumbnail-button {
    position: relative;
    overflow: hidden;
  }

  .thumbnail-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  .thumbnail-button:hover::before {
    left: 100%;
  }

  /* 缩略图导航栏渐变背景动画 */
  .thumbnail-nav-bg {
    background: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.9) 0%,
      rgba(0, 0, 0, 0.7) 40%,
      rgba(0, 0, 0, 0.3) 70%,
      transparent 100%
    );
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  /* 响应式缩略图尺寸和动画优化 */
  @media (max-width: 640px) {
    .thumbnail-scroll {
      gap: 0.5rem;
      padding-bottom: 0.25rem;
    }

    .thumbnail-button {
      width: 3rem;
      height: 3rem;
    }

    /* 移动端优化的缩略图导航栏 */
    .thumbnail-nav-bg {
      padding: 1rem 0.5rem;
    }
  }

  @media (min-width: 641px) and (max-width: 1024px) {
    .thumbnail-scroll {
      gap: 0.625rem;
      padding-bottom: 0.375rem;
    }

    .thumbnail-button {
      width: 3.5rem;
      height: 3.5rem;
    }
  }

  /* 文本截断样式 */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 相册卡片统一样式 */
  .album-card-fixed-height {
    height: 26.25rem; /* 420px */
    display: flex;
    flex-direction: column;
  }

  .album-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .album-card-description {
    height: 2.5rem; /* 40px - 固定2行文字高度 */
    overflow: hidden;
  }

  /* 相册卡片悬停优化 */
  .album-card-container {
    position: relative;
    transition: z-index 0.3s ease;
  }

  .album-card-container:hover {
    z-index: 10;
  }

  /* 防止悬停时内容被截断 */
  .album-grid-container {
    padding: 12px 0;
  }

  .album-grid-item {
    padding: 12px 0;
    position: relative;
  }

  /* 相册详情页专用样式 */
  .album-hero-bg {
    background:
      radial-gradient(circle at 30% 20%, rgba(16, 185, 129, 0.3), transparent 50%),
      radial-gradient(circle at 70% 80%, rgba(20, 184, 166, 0.2), transparent 50%),
      linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(20, 184, 166, 0.1) 100%);
  }

  .album-image-card {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .album-image-card:hover {
    transform: translateY(-8px) scale(1.01);
    box-shadow:
      0 20px 40px -12px rgba(16, 185, 129, 0.15),
      0 0 0 1px rgba(16, 185, 129, 0.05);
  }

  .album-masonry {
    column-fill: balance;
    column-gap: 1.5rem;
  }

  .album-masonry-item {
    break-inside: avoid;
    margin-bottom: 1.5rem;
    display: inline-block;
    width: 100%;
  }

  .lightbox-backdrop {
    backdrop-filter: blur(20px) saturate(180%);
    background: rgba(0, 0, 0, 0.95);
  }

  .lightbox-image-glow {
    filter: drop-shadow(0 0 20px rgba(16, 185, 129, 0.3));
  }

  /* 3D标题效果 */
  .album-title-3d {
    text-shadow:
      0 1px 0 rgba(255, 255, 255, 0.1),
      0 2px 0 rgba(255, 255, 255, 0.05),
      0 3px 0 rgba(255, 255, 255, 0.03),
      0 4px 8px rgba(0, 0, 0, 0.3);
  }

  /* 滚动进度条动画 */
  .scroll-progress {
    background: linear-gradient(90deg, #10B981, #14B8A6, #06B6D4);
    background-size: 200% 100%;
    animation: gradient-shift 3s ease infinite;
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* 图片加载动画 */
  .image-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
  }

  .dark .image-loading {
    background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%);
    background-size: 200% 100%;
  }

  @keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  /* 响应式优化 */
  @media (max-width: 640px) {
    .album-image-card:hover {
      transform: translateY(-4px) scale(1.005);
    }

    .album-masonry {
      column-count: 1;
    }
  }

  @media (min-width: 641px) and (max-width: 1024px) {
    .album-masonry {
      column-count: 2;
    }
  }

  @media (min-width: 1025px) {
    .album-masonry {
      column-count: 3;
    }
  }

  @media (min-width: 1280px) {
    .album-masonry {
      column-count: 4;
    }
  }

  /* 缩略图导航栏进入/退出动画 */
  .thumbnail-nav-enter {
    animation: slideUpFade 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .thumbnail-nav-exit {
    animation: slideDownFade 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  @keyframes slideUpFade {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideDownFade {
    from {
      transform: translateY(0);
      opacity: 1;
    }
    to {
      transform: translateY(100%);
      opacity: 0;
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes glowPulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(16, 185, 129, 0.6);
  }
}

@keyframes breathingSoft {
  0%, 100% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.02);
    opacity: 1;
  }
}

/* 瀑布流布局优化 */
.masonry-grid {
  column-count: 1;
  column-gap: 1rem;
}

@media (min-width: 640px) {
  .masonry-grid {
    column-count: 2;
  }
}

@media (min-width: 768px) {
  .masonry-grid {
    column-count: 3;
  }
}

@media (min-width: 1024px) {
  .masonry-grid {
    column-count: 4;
  }
}

@media (min-width: 1280px) {
  .masonry-grid {
    column-count: 5;
  }
}

.masonry-item {
  break-inside: avoid;
  margin-bottom: 1rem;
}

/* 滚动条美化 */
.gallery-scroll::-webkit-scrollbar {
  width: 6px;
}

.gallery-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.gallery-scroll::-webkit-scrollbar-thumb {
  background: rgba(16, 185, 129, 0.5);
  border-radius: 3px;
}

.gallery-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(16, 185, 129, 0.7);
}

/* 富文本编辑器样式支持 */
.rich-text-content {
  /* 使用霞鹜文楷字体 */
  font-family: 'LXGW WenKai', serif;

  /* 确保编辑器中的内联样式能够生效 */
  & * {
    font-family: inherit;
  }
  
  /* 支持常见编辑器字体 */
  & [style*="font-family"] {
    font-family: var(--custom-font-family, inherit) !important;
  }
  
  /* 字体大小支持 */
  & [style*="font-size"] {
    font-size: var(--custom-font-size, inherit) !important;
  }
  
  /* 常见字体兼容性定义 */
  & [style*="Arial"], & [style*="arial"] {
    --custom-font-family: Arial, sans-serif;
  }
  
  & [style*="Helvetica"], & [style*="helvetica"] {
    --custom-font-family: Helvetica, Arial, sans-serif;
  }
  
  & [style*="Times New Roman"], & [style*="times new roman"], & [style*="Times"], & [style*="times"] {
    --custom-font-family: 'Times New Roman', Times, serif;
  }
  
  & [style*="Courier New"], & [style*="courier new"], & [style*="Courier"], & [style*="courier"] {
    --custom-font-family: 'Courier New', Courier, monospace;
  }
  
  & [style*="Georgia"], & [style*="georgia"] {
    --custom-font-family: Georgia, serif;
  }
  
  & [style*="Garamond"], & [style*="garamond"] {
    --custom-font-family: Garamond, serif;
  }
  
  & [style*="Verdana"], & [style*="verdana"] {
    --custom-font-family: Verdana, Geneva, sans-serif;
  }
  
  & [style*="Tahoma"], & [style*="tahoma"] {
    --custom-font-family: Tahoma, Geneva, sans-serif;
  }
  
  & [style*="Trebuchet MS"], & [style*="trebuchet ms"], & [style*="Trebuchet"], & [style*="trebuchet"] {
    --custom-font-family: 'Trebuchet MS', Helvetica, sans-serif;
  }
  
  & [style*="Impact"], & [style*="impact"] {
    --custom-font-family: Impact, Charcoal, sans-serif;
  }
  
  & [style*="Comic Sans MS"], & [style*="comic sans ms"], & [style*="Comic Sans"], & [style*="comic sans"] {
    --custom-font-family: 'Comic Sans MS', cursive, sans-serif;
  }

  /* 中文字体支持 - 优先使用霞鹜文楷 */
  & [style*="宋体"], & [style*="SimSun"] {
    --custom-font-family: "LXGW WenKai", "宋体", SimSun, serif;
  }

  & [style*="黑体"], & [style*="SimHei"] {
    --custom-font-family: "LXGW WenKai", "黑体", SimHei, sans-serif;
  }

  & [style*="微软雅黑"], & [style*="Microsoft YaHei"] {
    --custom-font-family: "LXGW WenKai", "Microsoft YaHei", sans-serif;
  }

  & [style*="楷体"], & [style*="KaiTi"] {
    --custom-font-family: "LXGW WenKai", "楷体", KaiTi, cursive;
  }

  & [style*="仿宋"], & [style*="FangSong"] {
    --custom-font-family: "LXGW WenKai", "仿宋", FangSong, serif;
  }

  /* 霞鹜文楷字体支持 */
  & [style*="LXGW WenKai"], & [style*="霞鹜文楷"] {
    --custom-font-family: "LXGW WenKai", serif;
  }
  
  /* 表格样式支持 */
  & table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 1rem;
  }
  
  & table td,
  & table th {
    border: 1px solid #e5e7eb;
    padding: 0.75rem;
    text-align: left;
  }
  
  & table th {
    background-color: #f9fafb;
    font-weight: 600;
  }
  
  & table tr:nth-child(even) {
    background-color: #f3f4f6;
  }
  
  /* 排版样式支持 */
  & .ql-align-center {
    text-align: center;
  }
  
  & .ql-align-right {
    text-align: right;
  }
  
  & .ql-align-justify {
    text-align: justify;
  }
  
  & .ql-indent-1 {
    padding-left: 3em;
  }
  
  & .ql-indent-2 {
    padding-left: 6em;
  }

  /* Markdown渲染增强样式 */
  & h1, & h2, & h3, & h4, & h5, & h6 {
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    line-height: 1.25;
  }

  & h1 {
    font-size: 2rem;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.5rem;
  }

  & h2 {
    font-size: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.25rem;
  }

  & h3 {
    font-size: 1.25rem;
  }

  & h4 {
    font-size: 1.125rem;
  }

  & p {
    margin-bottom: 1rem;
    line-height: 1.6;
  }

  & ul, & ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }

  & li {
    margin-bottom: 0.25rem;
  }

  & blockquote {
    border-left: 4px solid #e5e7eb;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    color: #6b7280;
  }

  & code {
    background-color: #f3f4f6;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-family: 'Courier New', Courier, monospace;
  }

  & pre {
    background-color: #1f2937;
    color: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1rem 0;
  }

  & pre code {
    background-color: transparent;
    padding: 0;
    color: inherit;
  }

  & hr {
    border: none;
    border-top: 2px solid #e5e7eb;
    margin: 2rem 0;
  }

  & img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1rem 0;
  }

  & a {
    color: #3b82f6;
    text-decoration: underline;
  }

  & a:hover {
    color: #1d4ed8;
  }

  /* 暗色模式支持 */
  .dark & {
    & h1, & h2 {
      border-color: #374151;
    }

    & blockquote {
      border-color: #374151;
      color: #9ca3af;
    }

    & code {
      background-color: #374151;
      color: #f9fafb;
    }

    & hr {
      border-color: #374151;
    }

    & table td,
    & table th {
      border-color: #374151;
    }

    & table th {
      background-color: #374151;
    }

    & table tr:nth-child(even) {
      background-color: #1f2937;
    }
  }
}

/* 标签悬停效果 */
@layer components {
  .tag-hover-effect:not(.tag-selected):hover {
    background-color: var(--hover-bg) !important;
    color: var(--hover-color) !important;
    border-color: var(--hover-bg) !important;
  }
}

/* Hero区域增强动画 */
@layer utilities {
  /* 新增Hero区域动画 */
  @keyframes glow-pulse {
    0%, 100% {
      opacity: 0.5;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  @keyframes text-glow {
    0%, 100% {
      text-shadow: 0 0 5px rgba(var(--primary), 0.3);
    }
    50% {
      text-shadow: 0 0 20px rgba(var(--primary), 0.6), 0 0 30px rgba(var(--primary), 0.4);
    }
  }

  @keyframes float-gentle {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-3px);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* 径向渐变背景 */
  .bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }

  /* 动画类 */
  .animate-glow-pulse {
    animation: glow-pulse 3s ease-in-out infinite;
  }

  .animate-text-glow {
    animation: text-glow 2s ease-in-out infinite;
  }

  .animate-float-gentle {
    animation: float-gentle 3s ease-in-out infinite;
  }

  .animate-shimmer {
    animation: shimmer 2s linear infinite;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    background-size: 200% 100%;
  }

  /* Enhanced Blog Module Animations */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out forwards;
  }

  .animate-pulse-soft {
    animation: pulseSoft 2s ease-in-out infinite;
  }

  .animate-bounce-soft {
    animation: bounceSoft 0.6s ease-in-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out forwards;
  }
}

/* Enhanced Blog Module Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulseSoft {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes bounceSoft {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
