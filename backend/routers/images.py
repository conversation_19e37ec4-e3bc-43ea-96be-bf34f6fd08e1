from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete, and_, desc, func, or_
from sqlalchemy.orm import selectinload
from typing import List, Optional, Annotated
from database.database import get_db
from models import Image, Tag, image_tags, Album, album_images
from models.image import ImageCategory

from schemas.schemas import (ImageCreate, ImageResponse, ImageUpdate,
                           TagCreate, TagResponse, TimelineResponse,
                           AlbumResponse, AlbumCreate, AlbumUpdate,
                           AlbumCategoryResponse, AlbumListResponse,
                           ImageBatchUpdate, ImageBatchDelete, ImageSearchRequest,
                           ImageImportFromUrl)
from app.utils.slug_utils import generate_slug
import datetime
import os
import shutil
import uuid
from app.config import settings
from utils.auth import get_current_user
from utils.image_processing import save_image, process_image, cleanup_temp_files
from pathlib import Path
from fastapi.responses import FileResponse
from pydantic import BaseModel, HttpUrl

router = APIRouter(
    prefix="/images",
    tags=["images"],
    responses={404: {"description": "Not found"}},
)

class ImageUrlImport(BaseModel):
    urls: List[HttpUrl]
    album_id: Optional[int] = None
    tags: Optional[str] = None
    location: Optional[str] = None
    content: Optional[str] = None

class ImageDirectUrlImport(BaseModel):
    url: HttpUrl
    display_name: Optional[str] = None
    description: Optional[str] = None
    category_id: Optional[int] = None


@router.post("/", response_model=ImageResponse, status_code=status.HTTP_201_CREATED)
async def create_image(
    url: str = Form(...),
    alt: str = Form(...),
    width: int = Form(...),
    height: int = Form(...),
    date: str = Form(...),
    location: Optional[str] = Form(None),
    caption: Optional[str] = Form(None),
    storage_type: str = Form("local"),
    tags: Optional[str] = Form(None),
    thumbnail_url: Optional[str] = Form(None),
    webp_url: Optional[str] = Form(None),
    thumbnail_webp_url: Optional[str] = Form(None),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """创建新图片记录"""
    # 解析日期
    try:
        parsed_date = datetime.date.fromisoformat(date)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid date format. Use YYYY-MM-DD"
        )
    
    # 解析标签
    tag_list = []
    if tags:
        tag_list = [tag.strip() for tag in tags.split(',')]
    
    # 创建图片记录
    db_image = Image(
        url=url,
        thumbnail_url=thumbnail_url or url,
        webp_url=webp_url,
        thumbnail_webp_url=thumbnail_webp_url,
        alt=alt,
        width=width,
        height=height,
        date=parsed_date,
        location=location,
        caption=caption,
        storage_type=storage_type
    )
    
    db.add(db_image)
    await db.commit()
    await db.refresh(db_image)
    
    # 处理标签
    if tag_list:
        for tag_name in tag_list:
            # 检查标签是否存在
            result = await db.execute(select(Tag).where(Tag.name == tag_name))
            tag = result.scalars().first()
            
            if not tag:
                # 创建新标签
                tag = Tag(name=tag_name)
                db.add(tag)
                await db.commit()
                await db.refresh(tag)
            
            # 添加图片-标签关联
            await db.execute(
                image_tags.insert().values(
                    image_id=db_image.id,
                    tag_id=tag.id
                )
            )
    
    await db.commit()
    
    # 获取完整的图片信息（包括标签）
    result = await db.execute(
        select(Image)
        .where(Image.id == db_image.id)
        .options(
            selectinload(Image.tags)
        )
    )
    created_image = result.scalars().first()
    
    return created_image

@router.post("/upload-simple", response_model=dict)
async def upload_simple_image(
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """简化的图片上传，专门用于博客编辑器"""
    try:
        # 检查文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只支持图片文件")

        # 生成唯一文件名
        file_extension = file.filename.split('.')[-1] if '.' in file.filename else 'jpg'
        unique_filename = f"{uuid.uuid4()}.{file_extension}"

        # 确保上传目录存在
        upload_dir = Path(settings.UPLOAD_DIR) / "blog-images"
        upload_dir.mkdir(parents=True, exist_ok=True)

        # 保存文件
        file_path = upload_dir / unique_filename
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # 生成访问URL
        file_url = f"/uploads/blog-images/{unique_filename}"

        return {
            "success": True,
            "url": file_url,
            "filename": unique_filename,
            "message": "图片上传成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"图片上传失败: {str(e)}")

@router.post("/upload-manager", response_model=ImageResponse)
async def upload_image_manager(
    file: UploadFile = File(...),
    display_name: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    category_id: Optional[int] = Form(None),
    # 图片处理选项
    enable_compression: bool = Form(True),
    enable_webp: bool = Form(True),
    enable_thumbnail: bool = Form(True),
    quality: int = Form(85),
    max_width: int = Form(1920),
    max_height: int = Form(1080),
    thumbnail_size: int = Form(300),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """图片管理系统专用上传接口"""
    print(f"Image manager upload request:")
    print(f"  File: {file.filename}")
    print(f"  Display name: {display_name}")
    print(f"  Description: {description}")
    print(f"  Category ID: {category_id}")


    try:
        # 检查文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只支持图片文件")

        # 验证分类是否存在
        if category_id:
            result = await db.execute(
                select(ImageCategory).where(ImageCategory.id == category_id)
            )
            category = result.scalars().first()
            if not category:
                raise HTTPException(status_code=400, detail="指定的分类不存在")

        # 生成唯一文件名
        file_extension = file.filename.split('.')[-1] if '.' in file.filename else 'jpg'
        unique_filename = f"{uuid.uuid4()}.{file_extension}"

        # 确保上传目录存在
        upload_dir = Path(settings.UPLOAD_DIR) / "images"
        upload_dir.mkdir(parents=True, exist_ok=True)

        # 保存文件
        file_path = upload_dir / unique_filename
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # 使用高级图片处理
        from utils.image_processing import process_image_advanced

        processing_options = {
            'create_webp': enable_webp,
            'create_thumbnail': enable_thumbnail,
            'create_progressive': False,  # 对于管理上传，不需要多尺寸
            'compress': enable_compression,
            'max_width': max_width,
            'max_height': max_height,
            'quality': max(10, min(100, quality)),  # 限制质量范围
            'thumbnail_size': (thumbnail_size, thumbnail_size)
        }

        # 处理图片
        processed_result = await process_image_advanced(file_path, processing_options)

        # 生成访问URL
        file_url = f"/uploads/images/{unique_filename}"

        # 处理后的文件URL
        processed_files = processed_result.get('processed_files', {})
        thumbnail_url = f"/uploads/images/{Path(processed_files.get('thumbnail', file_path)).name}" if 'thumbnail' in processed_files else file_url
        webp_url = f"/uploads/images/{Path(processed_files.get('webp', file_path)).name}" if 'webp' in processed_files else file_url
        thumbnail_webp_url = f"/uploads/images/{Path(processed_files.get('thumbnail_webp', file_path)).name}" if 'thumbnail_webp' in processed_files else thumbnail_url

        # 创建数据库记录
        db_image = Image(
            url=file_url,
            thumbnail_url=thumbnail_url,
            webp_url=webp_url,
            thumbnail_webp_url=thumbnail_webp_url,
            alt=display_name or file.filename or "Uploaded image",
            width=processed_result.get('width', 0),
            height=processed_result.get('height', 0),
            date=datetime.datetime.now().date(),
            display_name=display_name,
            description=description,
            category_id=category_id,
            file_size=file.size,
            mime_type=file.content_type,
            original_filename=file.filename,
            image_metadata=processed_result.get('metadata', {}),
            upload_source='local',
            storage_type='local'
        )

        db.add(db_image)
        await db.commit()
        await db.refresh(db_image)

        # 手动构建响应数据，避免关联查询问题
        response_data = {
            "id": db_image.id,
            "url": db_image.url,
            "thumbnail_url": db_image.thumbnail_url,
            "webp_url": db_image.webp_url,
            "thumbnail_webp_url": db_image.thumbnail_webp_url,
            "alt": db_image.alt,
            "width": db_image.width,
            "height": db_image.height,
            "date": db_image.date,
            "location": db_image.location,
            "caption": db_image.caption,
            "storage_type": db_image.storage_type,
            "display_name": db_image.display_name,
            "description": db_image.description,
            "category_id": db_image.category_id,
            "file_size": db_image.file_size,
            "mime_type": db_image.mime_type,
            "original_filename": db_image.original_filename,

            "upload_source": db_image.upload_source,
            "external_id": db_image.external_id,
            "image_metadata": db_image.image_metadata,
            "created_at": db_image.created_at,
            "updated_at": db_image.updated_at,
            "tags": [],
            "albums": [],
            "category": None
        }

        return response_data

    except Exception as e:
        print(f"Upload error: {e}")
        raise HTTPException(status_code=500, detail=f"图片上传失败: {str(e)}")


@router.post("/upload", response_model=List[ImageResponse])
async def upload_image(
    files: List[UploadFile] = File(...),
    content: Optional[str] = Form(None),
    location: Optional[str] = Form(None),
    tags: Optional[str] = Form(None),
    album_id: Optional[int] = Form(None),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """上传图片（原有接口，用于相册等功能）"""
    print(f"Upload request received:")
    print(f"  Files: {[f.filename for f in files]}")
    print(f"  Content: {content}")
    print(f"  Location: {location}")
    print(f"  Tags: {tags}")
    print(f"  Album ID: {album_id}")

    uploaded_images = []
    
    # 获取相册信息（如果有）
    album = None
    if album_id:
        result = await db.execute(select(Album).where(Album.id == album_id))
        album = result.scalars().first()
        if not album:
            raise HTTPException(status_code=404, detail="Album not found")
    
    # 处理标签
    tag_list = []
    if tags:
        tag_names = [t.strip() for t in tags.split(',') if t.strip()]
        for tag_name in tag_names:
            # 查找或创建标签
            result = await db.execute(select(Tag).where(Tag.name == tag_name))
            tag = result.scalars().first()
            if not tag:
                tag = Tag(name=tag_name)
                db.add(tag)
                await db.commit()
            tag_list.append(tag)
    
    for file in files:
        try:
            # 确定存储位置
            current_year = datetime.datetime.now().strftime("%Y")
            album_name = album.title if album else None
            
            # 保存原始文件
            file_path = await save_image(file, current_year, album_name)
            
            # 处理图片（生成缩略图和WebP版本）
            image_info = await process_image(file_path, current_year, album_name)
            
            # 创建数据库记录
            db_image = Image(
                url=image_info["original_path"],
                thumbnail_url=image_info["thumbnail_path"],
                webp_url=image_info["webp_path"],
                thumbnail_webp_url=image_info["thumbnail_webp_path"],
                width=image_info["width"],
                height=image_info["height"],
                content=content,
                location=location,
                date=datetime.datetime.now().date()
            )
            
            # 添加标签
            if tag_list:
                db_image.tags.extend(tag_list)
            
            # 添加到相册
            if album:
                db_image.albums.append(album)
            
            db.add(db_image)
            await db.commit()
            await db.refresh(db_image)
            
            # 清理临时文件
            cleanup_temp_files(file_path)
            
            uploaded_images.append(db_image)
            
        except Exception as e:
            # 发生错误时回滚
            await db.rollback()
            raise HTTPException(status_code=500, detail=f"Error uploading image: {str(e)}")
    
    return uploaded_images

@router.get("/tags/all", response_model=List[TagResponse])
async def read_tags(
    db: AsyncSession = Depends(get_db)
):
    """获取所有标签"""
    result = await db.execute(select(Tag))
    tags = result.scalars().all()
    
    return tags

@router.get("/", response_model=List[ImageResponse])
async def read_images(
    skip: int = 0,
    limit: int = 100,
    tag: Optional[str] = None,
    location: Optional[str] = None,
    date_from: Optional[str] = None,
    date_to: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取图片列表"""
    query = select(Image).options(
        selectinload(Image.tags),
        selectinload(Image.albums),
        selectinload(Image.category)
    )
    
    # 应用筛选条件
    if tag:
        query = query.join(Image.tags).where(Tag.name == tag)
    
    if location:
        query = query.where(Image.location.ilike(f"%{location}%"))
    
    if date_from:
        try:
            from_date = datetime.date.fromisoformat(date_from)
            query = query.where(Image.date >= from_date)
        except ValueError:
            pass
    
    if date_to:
        try:
            to_date = datetime.date.fromisoformat(date_to)
            query = query.where(Image.date <= to_date)
        except ValueError:
            pass
    
    # 按日期降序排序
    query = query.order_by(desc(Image.date))
    
    # 分页
    query = query.offset(skip).limit(limit)
    
    result = await db.execute(query)
    images = result.scalars().all()
    
    # 检查缩略图是否存在，如果不存在则设为空
    for image in images:
        if image.thumbnail_url:
            thumbnail_path = settings.BASE_DIR / image.thumbnail_url.lstrip('/')
            if not thumbnail_path.exists():
                image.thumbnail_url = None
            
    return images

@router.get("/timeline", response_model=List[TimelineResponse])
async def get_timeline(
    skip: int = 0,
    limit: int = 20,
    db: AsyncSession = Depends(get_db)
):
    """获取时间线信息"""
    # 获取按日期降序排列的图片
    result = await db.execute(
        select(Image.date)
        .distinct()
        .order_by(desc(Image.date))
        .offset(skip).limit(limit)
    )
    dates = result.scalars().all()
    
    timeline = []
    
    for date in dates:
        # 获取该日期的图片
        result = await db.execute(
            select(Image)
            .where(Image.date == date)
            .options(
                selectinload(Image.tags)
            )
            .order_by(desc(Image.id))
        )
        images = result.scalars().all()
        
        # 检查缩略图是否存在
        for image in images:
            if image.thumbnail_url:
                thumbnail_path = settings.BASE_DIR / image.thumbnail_url.lstrip('/')
                if not thumbnail_path.exists():
                    image.thumbnail_url = None
        
        # 获取该日期的标签
        tags = set()
        for image in images:
            for tag in image.tags:
                tags.add(tag.name)
        
        # 获取该日期的位置
        locations = set()
        for image in images:
            if image.location:
                locations.add(image.location)
        
        # 构建时间线项
        timeline_item = {
            "date": date.isoformat(),
            "location": ", ".join(locations) if locations else None,
            "images": images,
            "tags": list(tags)
        }
        
        # 如果有图片包含内容，使用第一个有内容的图片的内容
        for image in images:
            if image.content:
                timeline_item["content"] = image.content
                break
        
        timeline.append(timeline_item)
    
    return timeline

@router.get("/albums", response_model=List[AlbumResponse])
async def get_albums(
    type: Optional[str] = None,
    category: Optional[str] = None,
    is_featured: Optional[bool] = None,
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_db)
):
    """获取相册列表 - 支持分类筛选和分页"""
    query = select(Album).options(
        selectinload(Album.cover_image).selectinload(Image.tags),
        selectinload(Album.cover_image).selectinload(Image.category),
        selectinload(Album.images).selectinload(Image.tags),
        selectinload(Album.images).selectinload(Image.category)
    )

    # 筛选条件
    if type and type != "all":
        query = query.where(Album.type == type)
    if category and category != "all":
        query = query.where(Album.category == category)
    if is_featured is not None:
        query = query.where(Album.is_featured == is_featured)

    # 排序：精选优先，然后按sort_order，最后按日期
    query = query.order_by(desc(Album.is_featured), Album.sort_order, desc(Album.date))

    # 分页
    offset = (page - 1) * size
    query = query.offset(offset).limit(size)

    result = await db.execute(query)
    albums = result.scalars().all()

    # 检查缩略图是否存在
    for album in albums:
        if album.cover_image and album.cover_image.thumbnail_url:
            thumbnail_path = settings.BASE_DIR / album.cover_image.thumbnail_url.lstrip('/')
            if not thumbnail_path.exists():
                album.cover_image.thumbnail_url = None

    return albums

@router.post("/albums", response_model=AlbumResponse)
async def create_album(
    title: str = Form(...),
    description: Optional[str] = Form(None),
    date: str = Form(...),
    location: Optional[str] = Form(None),
    type: str = Form("theme"),
    # 新增字段 - 整合Gallery功能
    category: Optional[str] = Form(None),
    slug: Optional[str] = Form(None),
    layout_type: str = Form("grid"),
    is_featured: bool = Form(False),
    sort_order: int = Form(0),
    cover: Optional[UploadFile] = File(None),
    files: Optional[List[UploadFile]] = File(None),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """创建新相册"""
    # 验证相册类型
    if type not in ['year', 'location', 'theme', 'category']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid album type. Must be one of: year, location, theme, category"
        )

    # 验证布局类型
    if layout_type not in ['grid', 'masonry', 'carousel']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid layout type. Must be one of: grid, masonry, carousel"
        )

    # 解析日期
    try:
        parsed_date = datetime.date.fromisoformat(date)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid date format. Use YYYY-MM-DD"
        )

    # 生成slug（如果没有提供）
    if not slug:
        # 获取现有的slug列表以确保唯一性
        existing_slugs_result = await db.execute(
            select(Album.slug).where(Album.slug.isnot(None))
        )
        existing_slugs = [row[0] for row in existing_slugs_result.fetchall()]

        # 生成唯一的slug
        slug = generate_slug(title, "album", existing_slugs)

    # 创建相册
    db_album = Album(
        title=title,
        description=description,
        date=parsed_date,
        location=location,
        type=type,
        # 新增字段
        category=category,
        slug=slug,
        layout_type=layout_type,
        is_featured=is_featured,
        sort_order=sort_order
    )
    db.add(db_album)
    await db.commit()
    await db.refresh(db_album)
    
    # 处理封面图片
    if cover:
        cover_image = await upload_image(
            files=[cover],
            content=None,
            location=location,
            tags=None,
            album_id=db_album.id,
            db=db,
            current_user=current_user
        )
        db_album.cover_image_id = cover_image[0].id
        await db.commit()
    
    # 处理其他图片
    if files:
        await upload_image(
            files=files,
            content=None,
            location=location,
            tags=None,
            album_id=db_album.id,
            db=db,
            current_user=current_user
        )
    
    # 获取完整的相册信息
    result = await db.execute(
        select(Album)
        .where(Album.id == db_album.id)
        .options(
            selectinload(Album.cover_image),
            selectinload(Album.images)
        )
    )
    created_album = result.scalars().first()
    return created_album

# ==================== 相册分类管理API ====================

@router.get("/albums/categories", response_model=List[AlbumCategoryResponse])
async def get_album_categories(db: AsyncSession = Depends(get_db)):
    """获取所有相册分类"""
    # 获取所有不同的分类及其相册数量
    result = await db.execute(
        select(Album.category, func.count(Album.id).label('album_count'))
        .where(Album.category.isnot(None))
        .group_by(Album.category)
        .order_by(Album.category)
    )
    categories = result.all()

    # 转换为响应格式
    category_responses = []
    for category, count in categories:
        category_responses.append({
            "id": hash(category) % 1000000,  # 生成一个简单的ID
            "name": category,
            "description": f"{category}相册",
            "album_count": count
        })

    return category_responses

@router.get("/albums/categories/{category_name}/albums", response_model=List[AlbumResponse])
async def get_albums_by_category(
    category_name: str,
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_db)
):
    """根据分类获取相册列表"""
    offset = (page - 1) * size

    result = await db.execute(
        select(Album)
        .where(Album.category == category_name)
        .options(
            selectinload(Album.cover_image).selectinload(Image.tags),
            selectinload(Album.cover_image).selectinload(Image.category),
            selectinload(Album.images).selectinload(Image.tags),
            selectinload(Album.images).selectinload(Image.category)
        )
        .order_by(Album.is_featured.desc(), Album.sort_order, Album.date.desc())
        .offset(offset)
        .limit(size)
    )
    albums = result.scalars().all()

    return albums

@router.get("/albums/{album_identifier}", response_model=AlbumResponse)
async def get_album(
    album_identifier: str,
    db: AsyncSession = Depends(get_db)
):
    """获取相册详情（支持ID或slug）"""
    # 尝试将identifier解析为整数ID
    try:
        album_id = int(album_identifier)
        query = select(Album).where(Album.id == album_id)
    except ValueError:
        # 如果不是数字，则作为slug查询
        query = select(Album).where(Album.slug == album_identifier)

    result = await db.execute(
        query.options(
            selectinload(Album.cover_image).selectinload(Image.tags),
            selectinload(Album.cover_image).selectinload(Image.category),
            selectinload(Album.images).selectinload(Image.tags),
            selectinload(Album.images).selectinload(Image.category)
        )
    )

    album = result.scalars().first()
    if not album:
        raise HTTPException(status_code=404, detail="Album not found")
    
    # 检查所有缩略图是否存在
    if album.cover_image and album.cover_image.thumbnail_url:
        thumbnail_path = settings.BASE_DIR / album.cover_image.thumbnail_url.lstrip('/')
        if not thumbnail_path.exists():
            album.cover_image.thumbnail_url = None
    
    for image in album.images:
        if image.thumbnail_url:
            thumbnail_path = settings.BASE_DIR / image.thumbnail_url.lstrip('/')
            if not thumbnail_path.exists():
                image.thumbnail_url = None
    
    return album

@router.put("/albums/{album_id}", response_model=AlbumResponse)
async def update_album(
    album_id: int,
    title: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    date: Optional[str] = Form(None),
    location: Optional[str] = Form(None),
    type: Optional[str] = Form(None),
    # 新增字段 - 整合Gallery功能
    category: Optional[str] = Form(None),
    slug: Optional[str] = Form(None),
    layout_type: Optional[str] = Form(None),
    is_featured: Optional[bool] = Form(None),
    sort_order: Optional[int] = Form(None),
    cover: Optional[UploadFile] = File(None),
    files: Optional[List[UploadFile]] = File(None),
    remove_image_ids: Optional[str] = Form(None),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """更新相册信息"""
    # 获取相册
    result = await db.execute(
        select(Album)
        .where(Album.id == album_id)
        .options(
            selectinload(Album.cover_image).selectinload(Image.tags),
            selectinload(Album.cover_image).selectinload(Image.category),
            selectinload(Album.images).selectinload(Image.tags),
            selectinload(Album.images).selectinload(Image.category)
        )
    )
    db_album = result.scalars().first()
    if not db_album:
        raise HTTPException(status_code=404, detail="Album not found")
    
    # 更新基本信息
    slug_needs_update = False
    if title is not None:
        db_album.title = title
        slug_needs_update = True
    if description is not None:
        db_album.description = description
    if date is not None:
        try:
            db_album.date = datetime.date.fromisoformat(date)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid date format. Use YYYY-MM-DD"
            )
    if location is not None:
        db_album.location = location
    if type is not None:
        if type not in ['year', 'location', 'theme', 'category']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid album type. Must be one of: year, location, theme, category"
            )
        db_album.type = type

    # 更新新增字段
    if category is not None:
        db_album.category = category
    if slug is not None:
        db_album.slug = slug
    elif slug_needs_update:
        # 如果标题发生变化但没有提供新的slug，自动生成
        existing_slugs_result = await db.execute(
            select(Album.slug).where(
                Album.slug.isnot(None),
                Album.id != album_id
            )
        )
        existing_slugs = [row[0] for row in existing_slugs_result.fetchall()]

        # 生成新的slug
        new_slug = generate_slug(db_album.title, "album", existing_slugs)
        db_album.slug = new_slug
    if layout_type is not None:
        if layout_type not in ['grid', 'masonry', 'carousel']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid layout type. Must be one of: grid, masonry, carousel"
            )
        db_album.layout_type = layout_type
    if is_featured is not None:
        db_album.is_featured = is_featured
    if sort_order is not None:
        db_album.sort_order = sort_order
    
    # 处理封面图片
    if cover:
        cover_image = await upload_image(
            files=[cover],
            content=None,
            location=location,
            tags=None,
            album_id=db_album.id,
            db=db,
            current_user=current_user
        )
        db_album.cover_image_id = cover_image[0].id
    
    # 处理其他图片
    if files:
        await upload_image(
            files=files,
            content=None,
            location=location,
            tags=None,
            album_id=db_album.id,
            db=db,
            current_user=current_user
        )
    
    # 移除图片
    if remove_image_ids:
        image_ids = [int(id.strip()) for id in remove_image_ids.split(',') if id.strip()]
        if image_ids:
            await db.execute(
                album_images.delete().where(
                    and_(
                        album_images.c.album_id == album_id,
                        album_images.c.image_id.in_(image_ids)
                    )
                )
            )
            # 如果删除的图片包含封面图片，需要重新设置封面
            if db_album.cover_image_id in image_ids:
                # 获取相册中的第一张图片作为新封面
                result = await db.execute(
                    select(Image.id)
                    .join(album_images)
                    .where(album_images.c.album_id == album_id)
                    .order_by(album_images.c.display_order)
                    .limit(1)
                )
                new_cover_id = result.scalar()
                db_album.cover_image_id = new_cover_id
    
    await db.commit()
    await db.refresh(db_album)
    
    return db_album

@router.delete("/albums/{album_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_album(
    album_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """删除相册"""
    # 检查相册是否存在
    result = await db.execute(select(Album).where(Album.id == album_id))
    album = result.scalars().first()
    
    if not album:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Album not found"
        )
    
    # 删除相册
    await db.execute(delete(Album).where(Album.id == album_id))
    await db.commit()
    
    return None

@router.get("/{image_id}", response_model=ImageResponse)
async def read_image(
    image_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取单张图片详情"""
    result = await db.execute(
        select(Image)
        .where(Image.id == image_id)
        .options(
            selectinload(Image.tags),
            selectinload(Image.albums)
        )
    )
    image = result.scalars().first()
    
    if not image:
        raise HTTPException(status_code=404, detail="Image not found")
    
    # 检查缩略图是否存在
    if image.thumbnail_url:
        thumbnail_path = settings.BASE_DIR / image.thumbnail_url.lstrip('/')
        if not thumbnail_path.exists():
            image.thumbnail_url = None
    
    return image

@router.delete("/batch-delete")
async def batch_delete_images(
    batch_delete: ImageBatchDelete,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """批量删除图片"""
    print(f"批量删除请求: image_ids={batch_delete.image_ids}")

    if not batch_delete.image_ids:
        raise HTTPException(status_code=400, detail="No image IDs provided")

    try:
        # 获取要删除的图片信息（用于删除文件和更新分类计数）
        images_result = await db.execute(
            select(Image).where(Image.id.in_(batch_delete.image_ids))
        )
        images_to_delete = images_result.scalars().all()

        # 收集需要更新计数的分类ID
        category_ids_to_update = set()
        for image in images_to_delete:
            if image.category_id:
                category_ids_to_update.add(image.category_id)

        # 删除相关的外键引用

        # 删除image_tags中的引用
        await db.execute(
            delete(image_tags).where(image_tags.c.image_id.in_(batch_delete.image_ids))
        )

        # 删除album_images中的引用
        await db.execute(
            delete(album_images).where(album_images.c.image_id.in_(batch_delete.image_ids))
        )

        # 删除数据库记录
        await db.execute(
            delete(Image).where(Image.id.in_(batch_delete.image_ids))
        )

        # 更新分类图片计数
        for category_id in category_ids_to_update:
            count_result = await db.execute(
                select(func.count(Image.id)).where(Image.category_id == category_id)
            )
            actual_count = count_result.scalar() or 0

            await db.execute(
                update(ImageCategory)
                .where(ImageCategory.id == category_id)
                .values(image_count=actual_count)
            )

        await db.commit()

        # 删除物理文件（在后台进行）
        for image in images_to_delete:
            try:
                if image.storage_type == "local" and image.url:
                    # 使用绝对路径
                    if image.url.startswith("/uploads/"):
                        file_path = Path(settings.UPLOAD_DIR) / os.path.basename(image.url)
                        if file_path.exists():
                            file_path.unlink()

                    # 删除相关文件
                    if image.thumbnail_url and image.thumbnail_url.startswith("/uploads/"):
                        thumb_path = Path(settings.UPLOAD_DIR) / os.path.basename(image.thumbnail_url)
                        if thumb_path.exists():
                            thumb_path.unlink()

                    if image.webp_url and image.webp_url.startswith("/uploads/"):
                        webp_path = Path(settings.UPLOAD_DIR) / os.path.basename(image.webp_url)
                        if webp_path.exists():
                            webp_path.unlink()

                    if image.thumbnail_webp_url and image.thumbnail_webp_url.startswith("/uploads/"):
                        thumb_webp_path = Path(settings.UPLOAD_DIR) / os.path.basename(image.thumbnail_webp_url)
                        if thumb_webp_path.exists():
                            thumb_webp_path.unlink()
            except Exception as e:
                # 文件删除失败不影响数据库操作
                print(f"Failed to delete file for image {image.id}: {str(e)}")

        return {
            "message": f"Successfully deleted {len(batch_delete.image_ids)} images",
            "deleted_count": len(batch_delete.image_ids)
        }

    except Exception as e:
        await db.rollback()
        print(f"批量删除图片错误: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error deleting images: {str(e)}")


@router.put("/batch-update")
async def batch_update_images(
    batch_update: ImageBatchUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """批量更新图片"""
    if not batch_update.image_ids:
        raise HTTPException(status_code=400, detail="No image IDs provided")

    # 构建更新数据
    update_data = {}
    if batch_update.category_id is not None:
        update_data['category_id'] = batch_update.category_id


    if not update_data:
        raise HTTPException(status_code=400, detail="No update data provided")

    try:
        # 更新图片
        await db.execute(
            update(Image)
            .where(Image.id.in_(batch_update.image_ids))
            .values(**update_data)
        )

        # 处理标签（如果提供）
        if batch_update.tags is not None:
            # 获取或创建标签
            tag_objects = []
            for tag_name in batch_update.tags:
                tag_result = await db.execute(select(Tag).where(Tag.name == tag_name))
                tag = tag_result.scalars().first()
                if not tag:
                    tag = Tag(name=tag_name)
                    db.add(tag)
                    await db.flush()
                tag_objects.append(tag)

            # 为每个图片设置标签
            for image_id in batch_update.image_ids:
                # 删除现有标签关联
                await db.execute(
                    delete(image_tags).where(image_tags.c.image_id == image_id)
                )

                # 添加新标签关联
                for tag in tag_objects:
                    await db.execute(
                        image_tags.insert().values(image_id=image_id, tag_id=tag.id)
                    )

        await db.commit()

        return {
            "message": f"Successfully updated {len(batch_update.image_ids)} images",
            "updated_count": len(batch_update.image_ids)
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating images: {str(e)}")


@router.delete("/{image_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_image(
    image_id: int,
    delete_file: bool = Query(False),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """删除图片"""
    print(f"单个删除请求: image_id={image_id}, delete_file={delete_file}")
    # 检查图片是否存在
    result = await db.execute(select(Image).where(Image.id == image_id))
    db_image = result.scalars().first()
    
    if not db_image:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Image with ID {image_id} not found"
        )
    
    # 如果需要删除文件
    if delete_file and db_image.storage_type == "local":
        try:
            # 删除原始文件
            if db_image.url and db_image.url.startswith("/uploads/"):
                file_path = os.path.join(settings.UPLOAD_DIR, os.path.basename(db_image.url))
                if os.path.exists(file_path):
                    os.remove(file_path)
            
            # 删除缩略图
            if db_image.thumbnail_url and db_image.thumbnail_url.startswith("/uploads/"):
                thumbnail_path = os.path.join(settings.UPLOAD_DIR, os.path.basename(db_image.thumbnail_url))
                if os.path.exists(thumbnail_path):
                    os.remove(thumbnail_path)
            
            # 删除WebP版本
            if db_image.webp_url and db_image.webp_url.startswith("/uploads/"):
                webp_path = os.path.join(settings.UPLOAD_DIR, os.path.basename(db_image.webp_url))
                if os.path.exists(webp_path):
                    os.remove(webp_path)
            
            # 删除WebP缩略图
            if db_image.thumbnail_webp_url and db_image.thumbnail_webp_url.startswith("/uploads/"):
                thumbnail_webp_path = os.path.join(settings.UPLOAD_DIR, os.path.basename(db_image.thumbnail_webp_url))
                if os.path.exists(thumbnail_webp_path):
                    os.remove(thumbnail_webp_path)
        except Exception as e:
            # 记录错误但继续删除数据库记录
            print(f"Error deleting image files: {str(e)}")
    
    # 记录分类ID用于更新计数
    category_id_to_update = db_image.category_id

    # 删除相关的外键引用

    # 删除image_tags中的引用
    await db.execute(
        delete(image_tags).where(image_tags.c.image_id == image_id)
    )

    # 删除album_images中的引用
    await db.execute(
        delete(album_images).where(album_images.c.image_id == image_id)
    )

    # 执行删除
    await db.execute(delete(Image).where(Image.id == image_id))

    # 更新分类图片计数
    if category_id_to_update:
        count_result = await db.execute(
            select(func.count(Image.id)).where(Image.category_id == category_id_to_update)
        )
        actual_count = count_result.scalar() or 0

        await db.execute(
            update(ImageCategory)
            .where(ImageCategory.id == category_id_to_update)
            .values(image_count=actual_count)
        )

    await db.commit()

    return None

@router.post("/import-urls", response_model=List[ImageResponse])
async def import_images_from_urls(
    import_data: ImageUrlImport,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """从对象存储URL导入图片"""
    imported_images = []
    
    # 获取相册信息（如果有）
    album = None
    if import_data.album_id:
        result = await db.execute(select(Album).where(Album.id == import_data.album_id))
        album = result.scalars().first()
        if not album:
            raise HTTPException(status_code=404, detail="Album not found")
    
    # 处理标签
    tag_list = []
    if import_data.tags:
        # 如果tags是列表，直接使用；如果是字符串，按逗号分割
        if isinstance(import_data.tags, list):
            tag_names = [t.strip() for t in import_data.tags if t.strip()]
        else:
            tag_names = [t.strip() for t in import_data.tags.split(',') if t.strip()]
            
        for tag_name in tag_names:
            # 查找或创建标签
            result = await db.execute(select(Tag).where(Tag.name == tag_name))
            tag = result.scalars().first()
            if not tag:
                tag = Tag(name=tag_name)
                db.add(tag)
                await db.commit()
            tag_list.append(tag)
    
    current_date = datetime.datetime.now().date()
    
    for url in import_data.urls:
        try:
            # 创建图片记录，设置默认值
            db_image = Image(
                url=str(url),
                thumbnail_url=str(url),  # 对象存储使用相同URL
                webp_url=str(url),      # 对象存储使用相同URL
                thumbnail_webp_url=str(url),  # 对象存储使用相同URL
                storage_type="object_storage",
                content=import_data.content,
                location=import_data.location,
                date=current_date,
                alt="Imported image",  # 默认alt文本
                width=800,            # 默认宽度
                height=600            # 默认高度
            )
            
            # 添加标签
            if tag_list:
                db_image.tags.extend(tag_list)
            
            # 添加到相册
            if album:
                db_image.albums.append(album)
            
            db.add(db_image)
            await db.commit()
            await db.refresh(db_image)
            
            imported_images.append(db_image)
            
        except Exception as e:
            await db.rollback()
            raise HTTPException(status_code=500, detail=f"Error importing image: {str(e)}")
    
    return imported_images


# 新增的图片管理API端点

@router.post("/search", response_model=dict)
async def search_images(
    search_request: ImageSearchRequest,
    db: AsyncSession = Depends(get_db)
):
    """搜索图片"""
    query = select(Image).options(selectinload(Image.category))

    # 构建搜索条件
    conditions = []

    # 文本搜索
    if search_request.query:
        text_conditions = []
        search_term = f"%{search_request.query}%"
        text_conditions.append(Image.display_name.ilike(search_term))
        text_conditions.append(Image.description.ilike(search_term))
        text_conditions.append(Image.alt.ilike(search_term))
        text_conditions.append(Image.original_filename.ilike(search_term))
        conditions.append(or_(*text_conditions))

    # 分类过滤
    if search_request.category_id:
        conditions.append(Image.category_id == search_request.category_id)



    # 日期范围过滤
    if search_request.date_from:
        conditions.append(Image.date >= search_request.date_from)
    if search_request.date_to:
        conditions.append(Image.date <= search_request.date_to)

    # 应用条件
    if conditions:
        query = query.where(and_(*conditions))

    # 排序
    if search_request.sort_by == "created_at":
        order_column = Image.created_at
    elif search_request.sort_by == "date":
        order_column = Image.date
    elif search_request.sort_by == "display_name":
        order_column = Image.display_name
    elif search_request.sort_by == "file_size":
        order_column = Image.file_size
    else:
        order_column = Image.created_at

    if search_request.sort_order == "desc":
        query = query.order_by(desc(order_column))
    else:
        query = query.order_by(order_column)

    # 计算总数
    count_query = select(func.count(Image.id))
    if conditions:
        count_query = count_query.where(and_(*conditions))

    total_result = await db.execute(count_query)
    total = total_result.scalar()

    # 分页 - 优先使用skip和limit参数
    if search_request.skip is not None and search_request.limit is not None:
        offset = search_request.skip
        limit = search_request.limit
    else:
        offset = (search_request.page - 1) * search_request.page_size
        limit = search_request.page_size

    query = query.offset(offset).limit(limit)

    result = await db.execute(query)
    images = result.scalars().all()

    # 手动构建响应数据，避免序列化问题
    image_list = []
    for image in images:
        image_data = {
            "id": image.id,
            "url": image.url,
            "thumbnail_url": image.thumbnail_url,
            "webp_url": image.webp_url,
            "thumbnail_webp_url": image.thumbnail_webp_url,
            "alt": image.alt or "",
            "width": image.width or 0,
            "height": image.height or 0,
            "date": image.date,
            "location": image.location,
            "caption": image.caption,
            "storage_type": image.storage_type or "local",
            "display_name": image.display_name,
            "description": image.description,
            "category_id": image.category_id,
            "file_size": image.file_size,
            "mime_type": image.mime_type,
            "original_filename": image.original_filename,

            "upload_source": image.upload_source or "local",
            "external_id": image.external_id,
            "image_metadata": image.image_metadata,
            "created_at": image.created_at,
            "updated_at": image.updated_at,
            "view_count": image.view_count or 0,
            "download_count": image.download_count or 0,
            "tags": [],  # 暂时不加载标签，避免关联查询问题
            "category": {
                "id": image.category.id,
                "name": image.category.name,
                "slug": image.category.slug,
                "color": image.category.color
            } if image.category else None
        }
        image_list.append(image_data)

    # 计算分页信息
    if search_request.skip is not None and search_request.limit is not None:
        current_page = (search_request.skip // search_request.limit) + 1
        page_size = search_request.limit
    else:
        current_page = search_request.page
        page_size = search_request.page_size

    return {
        "items": image_list,
        "total": total,
        "page": current_page,
        "page_size": page_size,
        "total_pages": (total + page_size - 1) // page_size
    }





# 图床集成API端点

@router.post("/import-direct-url", response_model=ImageResponse)
async def import_image_from_direct_url(
    import_data: ImageDirectUrlImport,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """从直接URL导入图片（不下载，直接保存URL）"""
    import aiohttp
    from urllib.parse import urlparse

    try:
        # 验证URL并获取图片信息
        async with aiohttp.ClientSession() as session:
            async with session.head(str(import_data.url)) as response:
                if response.status != 200:
                    raise HTTPException(status_code=400, detail=f"无法访问图片URL: HTTP {response.status}")

                content_type = response.headers.get('content-type', '')
                if not content_type.startswith('image/'):
                    raise HTTPException(status_code=400, detail="URL不是有效的图片")

                content_length = response.headers.get('content-length')
                file_size = int(content_length) if content_length else 0

        # 从URL提取文件名
        parsed_url = urlparse(str(import_data.url))
        original_filename = parsed_url.path.split('/')[-1] or 'imported_image'

        # 生成显示名称
        display_name = import_data.display_name or original_filename.rsplit('.', 1)[0]

        # 创建图片记录
        db_image = Image(
            url=str(import_data.url),
            thumbnail_url=str(import_data.url),  # 使用相同URL
            webp_url=str(import_data.url),
            thumbnail_webp_url=str(import_data.url),
            alt=display_name,
            width=0,  # 默认值，可以后续通过JS获取
            height=0,  # 默认值，可以后续通过JS获取
            date=datetime.datetime.now().date(),
            display_name=display_name,
            description=import_data.description,
            category_id=import_data.category_id,
            file_size=file_size,
            mime_type=content_type,
            original_filename=original_filename,

            upload_source='url',
            storage_type='external',
            external_id=str(import_data.url)
        )

        db.add(db_image)
        await db.commit()
        await db.refresh(db_image)

        # 更新分类图片计数
        if import_data.category_id:
            count_result = await db.execute(
                select(func.count(Image.id)).where(Image.category_id == import_data.category_id)
            )
            actual_count = count_result.scalar() or 0

            await db.execute(
                update(ImageCategory)
                .where(ImageCategory.id == import_data.category_id)
                .values(image_count=actual_count)
            )
            await db.commit()

        return db_image

    except aiohttp.ClientError as e:
        raise HTTPException(status_code=400, detail=f"网络错误: {str(e)}")
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")


@router.post("/import-from-urls")
async def import_images_from_urls(
    import_request: ImageImportFromUrl,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """从URL批量导入图片"""
    from utils.image_bed_service import image_bed_manager

    if not import_request.urls:
        raise HTTPException(status_code=400, detail="No URLs provided")

    results = []

    for url in import_request.urls:
        try:
            # 从URL导入图片
            import_result = await image_bed_manager.import_from_url(url)

            if import_result['success']:
                # 保存到数据库
                image_data = {
                    'url': import_result['url'],
                    'original_filename': Path(url).name or 'imported_image',
                    'display_name': import_request.description or Path(url).name,
                    'description': import_request.description,
                    'category_id': import_request.category_id,
                    'file_size': import_result.get('size', 0),
                    'mime_type': import_result.get('mime_type', 'image/jpeg'),
                    'width': import_result.get('width', 0),
                    'height': import_result.get('height', 0),

                    'upload_source': 'url',
                    'external_id': import_result.get('key'),
                    'storage_type': import_result.get('provider', 'url'),
                    'image_metadata': import_result.get('image_info', {}),
                    'date': datetime.now().date()
                }

                db_image = Image(**image_data)
                db.add(db_image)
                await db.flush()

                # 处理标签
                if import_request.tags:
                    for tag_name in import_request.tags:
                        tag_result = await db.execute(select(Tag).where(Tag.name == tag_name))
                        tag = tag_result.scalars().first()
                        if not tag:
                            tag = Tag(name=tag_name)
                            db.add(tag)
                            await db.flush()

                        # 添加标签关联
                        await db.execute(
                            image_tags.insert().values(image_id=db_image.id, tag_id=tag.id)
                        )

                results.append({
                    'url': url,
                    'success': True,
                    'image_id': db_image.id,
                    'imported_url': import_result['url']
                })

            else:
                results.append({
                    'url': url,
                    'success': False,
                    'error': import_result['error']
                })

        except Exception as e:
            results.append({
                'url': url,
                'success': False,
                'error': str(e)
            })

    await db.commit()

    success_count = sum(1 for r in results if r['success'])

    return {
        'message': f"Successfully imported {success_count} out of {len(import_request.urls)} images",
        'results': results,
        'success_count': success_count,
        'total_count': len(import_request.urls)
    }


@router.post("/upload-to-image-bed")
async def upload_to_image_bed(
    file: UploadFile = File(...),
    provider: str = Form('qiniu'),
    category_id: Optional[int] = Form(None),
    display_name: Optional[str] = Form(None),
    description: Optional[str] = Form(None),

    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """上传图片到图床"""
    from utils.image_bed_service import image_bed_manager
    from utils.image_processing import validate_image_file, get_file_info

    # 验证文件
    if not validate_image_file(file):
        raise HTTPException(status_code=400, detail="Invalid image file")

    # 保存临时文件
    import tempfile
    with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as temp_file:
        content = await file.read()
        temp_file.write(content)
        temp_path = temp_file.name

    try:
        # 上传到图床
        upload_result = await image_bed_manager.upload_image(
            temp_path,
            provider_name=provider,
            filename=file.filename
        )

        if not upload_result['success']:
            raise HTTPException(status_code=500, detail=upload_result['error'])

        # 获取文件信息
        file_info = get_file_info(temp_path, file.filename)

        # 保存到数据库
        image_data = {
            'url': upload_result['url'],
            'original_filename': file.filename,
            'display_name': display_name or Path(file.filename).stem,
            'description': description,
            'category_id': category_id,
            'file_size': file_info['file_size'],
            'mime_type': file_info['mime_type'],
            'width': upload_result.get('width', 0),
            'height': upload_result.get('height', 0),

            'upload_source': 'image_bed',
            'external_id': upload_result.get('key'),
            'storage_type': provider,
            'image_metadata': upload_result.get('image_info', {}),
            'date': datetime.now().date()
        }

        db_image = Image(**image_data)
        db.add(db_image)
        await db.commit()
        await db.refresh(db_image)

        return {
            'message': 'Image uploaded to image bed successfully',
            'image': db_image,
            'provider': provider,
            'external_url': upload_result['url']
        }

    finally:
        # 清理临时文件
        os.unlink(temp_path)


@router.get("/image-bed/providers")
async def get_image_bed_providers():
    """获取可用的图床服务提供商"""
    from utils.image_bed_service import image_bed_manager

    providers = []
    for name, provider in image_bed_manager.providers.items():
        providers.append({
            'name': name,
            'display_name': {
                'qiniu': '七牛云',
                'aliyun_oss': '阿里云OSS',
                'tencent_cos': '腾讯云COS'
            }.get(name, name),
            'is_default': name == image_bed_manager.default_provider
        })

    return {
        'providers': providers,
        'default_provider': image_bed_manager.default_provider
    }


# 单个图片操作路由（放在最后以避免路由冲突）

@router.put("/{image_id}", response_model=ImageResponse)
async def update_image(
    image_id: int,
    image_update: ImageUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """更新图片信息"""
    # 检查图片是否存在
    result = await db.execute(select(Image).where(Image.id == image_id))
    db_image = result.scalars().first()

    if not db_image:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Image with ID {image_id} not found"
        )

    # 准备更新数据
    update_data = image_update.dict(exclude={"tags"}, exclude_unset=True)

    # 执行更新
    await db.execute(
        update(Image)
        .where(Image.id == image_id)
        .values(**update_data)
    )

    # 更新标签
    if image_update.tags is not None:
        # 删除现有标签关联
        await db.execute(
            delete(image_tags)
            .where(image_tags.c.image_id == image_id)
        )

        # 添加新标签关联
        for tag_name in image_update.tags:
            # 检查标签是否存在
            result = await db.execute(select(Tag).where(Tag.name == tag_name))
            tag = result.scalars().first()

            if not tag:
                # 创建新标签
                tag = Tag(name=tag_name)
                db.add(tag)
                await db.commit()
                await db.refresh(tag)

            # 添加图片-标签关联
            await db.execute(
                image_tags.insert().values(
                    image_id=image_id,
                    tag_id=tag.id
                )
            )

    await db.commit()

    # 获取更新后的图片
    result = await db.execute(
        select(Image)
        .where(Image.id == image_id)
        .options(
            selectinload(Image.tags),
            selectinload(Image.category)
        )
    )
    updated_image = result.scalars().first()

    return updated_image


