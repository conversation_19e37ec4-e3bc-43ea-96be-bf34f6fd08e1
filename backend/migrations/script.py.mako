"""${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}

Portfolio项目数据库迁移文件
此文件由Alembic自动生成，请仔细审查后再应用到生产环境。

变更说明:
- 请在此处详细描述本次迁移的具体变更内容
- 包括新增/修改/删除的表、字段、索引等
- 说明变更的业务背景和影响范围

注意事项:
- 在生产环境应用前请务必备份数据库
- 复杂的数据迁移建议分步执行
- 确保回滚操作的正确性和安全性
"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
${imports if imports else ""}

# revision identifiers, used by Alembic.
revision: str = ${repr(up_revision)}
down_revision: Union[str, None] = ${repr(down_revision)}
branch_labels: Union[str, Sequence[str], None] = ${repr(branch_labels)}
depends_on: Union[str, Sequence[str], None] = ${repr(depends_on)}


def upgrade() -> None:
    """升级数据库架构

    执行数据库结构的升级操作，包括：
    - 创建新表
    - 添加/修改字段
    - 创建/删除索引
    - 数据迁移等

    注意：请确保操作的幂等性和安全性
    """
    ${upgrades if upgrades else "pass"}


def downgrade() -> None:
    """降级数据库架构

    回滚upgrade()中的所有操作，包括：
    - 删除新创建的表
    - 移除新添加的字段
    - 恢复修改的字段
    - 回滚数据变更等

    注意：请确保回滚操作不会导致数据丢失
    """
    ${downgrades if downgrades else "pass"}
