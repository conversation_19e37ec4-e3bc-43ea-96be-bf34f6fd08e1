# Portfolio项目数据库迁移

这是Portfolio项目的Alembic数据库迁移配置目录。

## 目录结构

- `env.py` - Alembic环境配置文件，支持开发和生产环境
- `script.py.mako` - 迁移文件生成模板
- `versions/` - 数据库迁移版本文件目录
- `alembic.ini` - Alembic主配置文件（位于项目根目录）

## 使用方法

### 创建新迁移
```bash
# 自动生成迁移（推荐）
poetry run alembic revision --autogenerate -m "描述变更内容"

# 手动创建空迁移
poetry run alembic revision -m "描述变更内容"
```

### 应用迁移
```bash
# 升级到最新版本
poetry run alembic upgrade head

# 升级到指定版本
poetry run alembic upgrade <revision_id>

# 降级到指定版本
poetry run alembic downgrade <revision_id>
```

### 查看状态
```bash
# 查看当前版本
poetry run alembic current

# 查看迁移历史
poetry run alembic history

# 检查是否有待应用的迁移
poetry run alembic check
```

## 注意事项

1. **运行目录**: 所有alembic命令必须在`backend/`目录下运行
2. **环境配置**: 确保数据库连接配置正确（通过环境变量或配置文件）
3. **模型导入**: 新增模型后需要在`models/__init__.py`中导入以确保被alembic检测到
4. **审查迁移**: 自动生成的迁移文件应该仔细审查后再应用
5. **备份数据**: 在生产环境应用迁移前务必备份数据库

## 迁移规范

- 迁移文件命名格式: `YYYY_MM_DD_HHMM-<revision_id>_<description>.py`
- 每个迁移都应该有清晰的描述信息
- 复杂的数据迁移应该分步进行
- 确保每个迁移都有对应的回滚操作