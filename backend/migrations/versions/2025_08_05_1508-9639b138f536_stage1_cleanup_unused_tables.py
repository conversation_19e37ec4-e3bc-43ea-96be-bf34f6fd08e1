"""stage1_cleanup_unused_tables

Revision ID: 9639b138f536
Revises: d5ebd9584e9d
Create Date: 2025-08-05 15:08:15.475040+08:00

Portfolio项目数据库迁移文件
此文件由Alembic自动生成，请仔细审查后再应用到生产环境。

变更说明:
- 请在此处详细描述本次迁移的具体变更内容
- 包括新增/修改/删除的表、字段、索引等
- 说明变更的业务背景和影响范围

注意事项:
- 在生产环境应用前请务必备份数据库
- 复杂的数据迁移建议分步执行
- 确保回滚操作的正确性和安全性
"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9639b138f536'
down_revision: Union[str, None] = 'd5ebd9584e9d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """第一阶段清理：删除明确无用的表

    本阶段删除以下明确不再使用的表：
    1. 旧的项目分类系统表（已被统一标签系统替代）
    2. 旧的图标收藏表
    3. 独立的项目表（已合并到blogs表）
    4. 旧的分类表

    这些表在当前代码中完全没有被引用，删除是安全的。
    """

    # 删除旧的项目分类系统表
    op.drop_table('project_categories_association')
    op.drop_table('project_module_categories_association')
    op.drop_table('project_categories')
    op.drop_table('project_module_categories')
    op.drop_table('projects')  # 独立项目表，已合并到blogs

    # 删除旧的分类表（已被标签系统替代）
    op.drop_table('categories')

    # 删除旧的图标收藏表
    op.drop_table('icon_favorites_old')


def downgrade() -> None:
    """第一阶段清理回滚

    警告：此回滚操作会重新创建已删除的表，但数据将丢失！
    建议从备份恢复而不是使用此回滚操作。
    """

    # 重新创建categories表
    op.create_table('categories',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(100), nullable=True),
        sa.Column('slug', sa.String(100), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('color', sa.String(50), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # 重新创建icon_favorites_old表
    op.create_table('icon_favorites_old',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('session_id', sa.String(255), nullable=True),
        sa.Column('icon_library', sa.String(100), nullable=False),
        sa.Column('icon_key', sa.String(255), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # 重新创建projects表
    op.create_table('projects',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('title', sa.String(255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('url', sa.String(500), nullable=True),
        sa.Column('github_url', sa.String(500), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # 重新创建project_categories表
    op.create_table('project_categories',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(100), nullable=True),
        sa.Column('slug', sa.String(100), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # 重新创建project_module_categories表
    op.create_table('project_module_categories',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(100), nullable=True),
        sa.Column('slug', sa.String(100), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # 重新创建关联表
    op.create_table('project_categories_association',
        sa.Column('project_id', sa.Integer(), nullable=False),
        sa.Column('category_id', sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint('project_id', 'category_id')
    )

    op.create_table('project_module_categories_association',
        sa.Column('project_id', sa.Integer(), nullable=False),
        sa.Column('category_id', sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint('project_id', 'category_id')
    )
