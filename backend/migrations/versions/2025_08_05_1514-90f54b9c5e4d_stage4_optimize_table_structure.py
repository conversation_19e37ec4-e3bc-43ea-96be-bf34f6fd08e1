"""stage4_optimize_table_structure

Revision ID: 90f54b9c5e4d
Revises: 8b45d91a27b8
Create Date: 2025-08-05 15:14:46.675653+08:00

Portfolio项目数据库迁移文件
此文件由Alembic自动生成，请仔细审查后再应用到生产环境。

变更说明:
- 请在此处详细描述本次迁移的具体变更内容
- 包括新增/修改/删除的表、字段、索引等
- 说明变更的业务背景和影响范围

注意事项:
- 在生产环境应用前请务必备份数据库
- 复杂的数据迁移建议分步执行
- 确保回滚操作的正确性和安全性
"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '90f54b9c5e4d'
down_revision: Union[str, None] = '8b45d91a27b8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """第四阶段优化：优化表结构和索引

    本阶段进行以下优化：
    1. 添加必要的索引以提高查询性能
    2. 优化现有索引
    3. 清理无用的索引
    4. 优化字段类型和约束

    这些优化将提高数据库查询性能和存储效率。
    """

    # 为blogs表添加性能优化索引
    try:
        op.create_index('idx_blogs_article_type_published', 'blogs', ['article_type', 'published'])
        op.create_index('idx_blogs_display_date_published', 'blogs', ['display_date', 'published'])
        op.create_index('idx_blogs_featured_published', 'blogs', ['featured', 'published'])
    except Exception:
        pass  # 如果索引已存在，忽略错误

    # 为images表添加性能优化索引
    try:
        op.create_index('idx_images_date_created', 'images', ['date', 'created_at'])
    except Exception:
        pass

    # 为tags表添加性能优化索引
    try:
        op.create_index('idx_tags_category_name', 'tags', ['category', 'name'])
    except Exception:
        pass

    # 为albums表添加性能优化索引
    try:
        op.create_index('idx_albums_public_featured', 'albums', ['is_public', 'is_featured'])
    except Exception:
        pass

    # 为website_versions表添加性能优化索引
    try:
        op.create_index('idx_website_versions_published_date', 'website_versions', ['published_date'])
    except Exception:
        pass


def downgrade() -> None:
    """第四阶段优化回滚

    删除在优化阶段添加的索引。
    """

    # 删除blogs表的优化索引
    try:
        op.drop_index('idx_blogs_article_type_published', 'blogs')
        op.drop_index('idx_blogs_display_date_published', 'blogs')
        op.drop_index('idx_blogs_featured_published', 'blogs')
    except Exception:
        pass

    # 删除images表的优化索引
    try:
        op.drop_index('idx_images_date_created', 'images')
    except Exception:
        pass

    # 删除tags表的优化索引
    try:
        op.drop_index('idx_tags_category_name', 'tags')
    except Exception:
        pass

    # 删除albums表的优化索引
    try:
        op.drop_index('idx_albums_public_featured', 'albums')
    except Exception:
        pass

    # 删除website_versions表的优化索引
    try:
        op.drop_index('idx_website_versions_published_date', 'website_versions')
    except Exception:
        pass
