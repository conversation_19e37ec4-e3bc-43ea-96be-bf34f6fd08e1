"""add_missing_fields_to_images_and_albums

Revision ID: 0745e82f4e3d
Revises: 90f54b9c5e4d
Create Date: 2025-08-05 17:06:46.618358+08:00

Portfolio项目数据库迁移文件
此文件由Alembic自动生成，请仔细审查后再应用到生产环境。

变更说明:
- 请在此处详细描述本次迁移的具体变更内容
- 包括新增/修改/删除的表、字段、索引等
- 说明变更的业务背景和影响范围

注意事项:
- 在生产环境应用前请务必备份数据库
- 复杂的数据迁移建议分步执行
- 确保回滚操作的正确性和安全性
"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '0745e82f4e3d'
down_revision: Union[str, None] = '90f54b9c5e4d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """升级数据库架构

    执行数据库结构的升级操作，包括：
    - 创建新表
    - 添加/修改字段
    - 创建/删除索引
    - 数据迁移等

    注意：请确保操作的幂等性和安全性
    """
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('icon_favorites', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_icon_favorites_id'))
        batch_op.drop_index(batch_op.f('uq_icon_session_favorite'))
        batch_op.drop_index(batch_op.f('uq_icon_user_favorite'))

    op.drop_table('icon_favorites')
    with op.batch_alter_table('about_pages', schema=None) as batch_op:
        batch_op.alter_column('photo_position',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('photo_style',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('photo_size',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('now()'),
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('now()'),
               existing_nullable=True)

    with op.batch_alter_table('albums', schema=None) as batch_op:
        batch_op.add_column(sa.Column('type', sa.Enum('year', 'location', 'theme', 'category'), nullable=True))
        batch_op.add_column(sa.Column('layout_type', sa.String(length=50), nullable=True, comment='布局类型: grid, masonry, carousel'))
        batch_op.alter_column('category',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=100),
               comment='相册分类',
               existing_nullable=True)
        batch_op.alter_column('slug',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=255),
               type_=sa.String(length=100),
               comment='URL友好名称',
               existing_nullable=True)
        batch_op.alter_column('is_featured',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               comment='是否精选',
               existing_nullable=True)
        batch_op.alter_column('sort_order',
               existing_type=mysql.INTEGER(),
               server_default=None,
               comment='排序顺序',
               existing_nullable=True)
        batch_op.drop_index(batch_op.f('idx_albums_category'))
        batch_op.drop_index(batch_op.f('idx_albums_is_featured'))
        batch_op.drop_index(batch_op.f('idx_albums_is_public'))
        batch_op.drop_index(batch_op.f('idx_albums_public_featured'))
        batch_op.drop_index(batch_op.f('idx_albums_slug'))
        batch_op.drop_index(batch_op.f('idx_albums_sort_order'))
        batch_op.create_unique_constraint(None, ['slug'])
        batch_op.drop_column('is_public')

    with op.batch_alter_table('api_metrics', schema=None) as batch_op:
        batch_op.alter_column('timestamp',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('now()'),
               existing_nullable=True)

    with op.batch_alter_table('blog_versions', schema=None) as batch_op:
        batch_op.alter_column('display_date',
               existing_type=mysql.DATETIME(),
               comment=None,
               existing_comment='展示日期',
               existing_nullable=False)
        batch_op.alter_column('is_major_change',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               nullable=True)
        batch_op.drop_index(batch_op.f('idx_blog_versions_is_major_change'))

    with op.batch_alter_table('blogs', schema=None) as batch_op:
        batch_op.alter_column('display_date',
               existing_type=mysql.DATETIME(),
               comment=None,
               existing_comment='展示日期，用于前端显示和排序',
               existing_nullable=False)
        batch_op.alter_column('article_type',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=None,
               existing_nullable=False)
        batch_op.alter_column('is_github_project',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('is_open_source',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               comment=None,
               existing_comment='是否开源项目',
               existing_nullable=True)
        batch_op.alter_column('featured',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('display_order',
               existing_type=mysql.INTEGER(),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('homepage_order',
               existing_type=mysql.INTEGER(),
               server_default=None,
               comment=None,
               existing_comment='首页显示顺序',
               existing_nullable=True)
        batch_op.alter_column('published_at',
               existing_type=mysql.DATETIME(),
               comment=None,
               existing_comment='发布时间',
               existing_nullable=True)
        batch_op.drop_index(batch_op.f('ft_blogs_title_description'), mysql_prefix='FULLTEXT')
        batch_op.drop_index(batch_op.f('idx_article_type_featured'))
        batch_op.drop_index(batch_op.f('idx_article_type_open_source'))
        batch_op.drop_index(batch_op.f('idx_blogs_article_type'))
        batch_op.drop_index(batch_op.f('idx_blogs_article_type_published'))
        batch_op.drop_index(batch_op.f('idx_blogs_display_date_published'))
        batch_op.drop_index(batch_op.f('idx_blogs_featured_published'))
        batch_op.drop_index(batch_op.f('idx_blogs_homepage_display'))
        batch_op.drop_index(batch_op.f('idx_blogs_homepage_order'))
        batch_op.drop_index(batch_op.f('idx_blogs_type_published_homepage'))
        batch_op.drop_index(batch_op.f('idx_featured'))
        batch_op.drop_index(batch_op.f('idx_is_open_source'))
        batch_op.drop_index(batch_op.f('ix_blogs_date'))
        batch_op.create_index(batch_op.f('ix_blogs_article_type'), ['article_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_blogs_display_date'), ['display_date'], unique=False)

    with op.batch_alter_table('careers', schema=None) as batch_op:
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('now()'),
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('now()'),
               existing_nullable=True)

    with op.batch_alter_table('content_drafts', schema=None) as batch_op:
        batch_op.alter_column('status',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=20),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('version',
               existing_type=mysql.INTEGER(),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('auto_save_interval',
               existing_type=mysql.INTEGER(),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=None,
               existing_nullable=True)
        batch_op.drop_index(batch_op.f('idx_content_type'))
        batch_op.drop_index(batch_op.f('idx_created_by'))
        batch_op.drop_index(batch_op.f('idx_status'))
        batch_op.create_index(batch_op.f('ix_content_drafts_id'), ['id'], unique=False)
        batch_op.drop_constraint(batch_op.f('content_drafts_ibfk_3'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('content_drafts_ibfk_1'), type_='foreignkey')
        batch_op.drop_constraint(batch_op.f('content_drafts_ibfk_2'), type_='foreignkey')
        batch_op.create_foreign_key(None, 'content_templates', ['template_id'], ['id'])
        batch_op.create_foreign_key(None, 'users', ['created_by'], ['id'])
        batch_op.create_foreign_key(None, 'content_drafts', ['parent_draft_id'], ['id'])

    with op.batch_alter_table('content_templates', schema=None) as batch_op:
        batch_op.alter_column('template_type',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('is_active',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('is_default',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('is_system',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('usage_count',
               existing_type=mysql.INTEGER(),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=None,
               existing_nullable=True)
        batch_op.drop_index(batch_op.f('idx_is_active'))
        batch_op.create_index(batch_op.f('ix_content_templates_id'), ['id'], unique=False)
        batch_op.drop_constraint(batch_op.f('content_templates_ibfk_1'), type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['created_by'], ['id'])

    with op.batch_alter_table('educations', schema=None) as batch_op:
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('now()'),
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('now()'),
               existing_nullable=True)

    with op.batch_alter_table('images', schema=None) as batch_op:
        batch_op.add_column(sa.Column('category_id', sa.Integer(), nullable=True, comment='分类ID'))
        batch_op.add_column(sa.Column('upload_source', sa.Enum('local', 'url', 'image_bed', name='upload_source_enum'), nullable=True, comment='上传来源'))
        batch_op.add_column(sa.Column('external_id', sa.String(length=255), nullable=True, comment='外部服务ID'))
        batch_op.drop_index(batch_op.f('idx_display_name'))
        batch_op.drop_index(batch_op.f('idx_images_date_created'))
        batch_op.create_foreign_key(None, 'image_categories', ['category_id'], ['id'], ondelete='SET NULL')

    with op.batch_alter_table('layout_blocks', schema=None) as batch_op:
        batch_op.alter_column('enabled',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('display_order',
               existing_type=mysql.INTEGER(),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('page_type',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=None,
               existing_nullable=True)
        batch_op.drop_index(batch_op.f('block_id'))
        batch_op.drop_index(batch_op.f('idx_block_id'))
        batch_op.drop_index(batch_op.f('idx_display_order'))
        batch_op.drop_index(batch_op.f('idx_page_type'))
        batch_op.create_index(batch_op.f('ix_layout_blocks_block_id'), ['block_id'], unique=True)
        batch_op.create_index(batch_op.f('ix_layout_blocks_id'), ['id'], unique=False)

    with op.batch_alter_table('navigation_items', schema=None) as batch_op:
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('now()'),
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('now()'),
               existing_nullable=True)

    with op.batch_alter_table('page_layouts', schema=None) as batch_op:
        batch_op.alter_column('is_active',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('version',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=20),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=None,
               existing_nullable=True)
        batch_op.drop_index(batch_op.f('idx_is_active'))
        batch_op.drop_index(batch_op.f('idx_page_type'))
        batch_op.drop_index(batch_op.f('page_type'))
        batch_op.create_index(batch_op.f('ix_page_layouts_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_page_layouts_page_type'), ['page_type'], unique=True)

    with op.batch_alter_table('seo_settings', schema=None) as batch_op:
        batch_op.alter_column('setting_key',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=100),
               comment=None,
               existing_comment='SEO设置键名',
               existing_nullable=False)
        batch_op.alter_column('setting_value',
               existing_type=mysql.JSON(),
               comment=None,
               existing_comment='SEO设置值（JSON格式）',
               existing_nullable=True)
        batch_op.alter_column('setting_type',
               existing_type=mysql.ENUM('global', 'page', 'content', 'social', 'analytics', collation='utf8mb4_unicode_ci'),
               server_default=None,
               comment=None,
               existing_comment='SEO设置类型',
               existing_nullable=True)
        batch_op.alter_column('page_type',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               comment=None,
               existing_comment='页面类型（homepage, blog, project, about等）',
               existing_nullable=True)
        batch_op.alter_column('content_id',
               existing_type=mysql.INTEGER(),
               comment=None,
               existing_comment='内容ID（用于内容级别的SEO设置）',
               existing_nullable=True)
        batch_op.alter_column('is_active',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               nullable=False,
               comment=None,
               existing_comment='是否启用')
        batch_op.alter_column('priority',
               existing_type=mysql.INTEGER(),
               server_default=None,
               nullable=False,
               comment=None,
               existing_comment='优先级（数字越大优先级越高）')
        batch_op.alter_column('description',
               existing_type=mysql.TEXT(collation='utf8mb4_unicode_ci'),
               comment=None,
               existing_comment='SEO设置描述',
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.TIMESTAMP(),
               server_default=None,
               type_=sa.DateTime(),
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=mysql.TIMESTAMP(),
               server_default=None,
               type_=sa.DateTime(),
               existing_nullable=True)
        batch_op.drop_index(batch_op.f('idx_content_id'))
        batch_op.drop_index(batch_op.f('idx_is_active'))
        batch_op.drop_index(batch_op.f('idx_page_type'))
        batch_op.drop_index(batch_op.f('idx_setting_key'))
        batch_op.drop_index(batch_op.f('idx_setting_type'))
        batch_op.drop_index(batch_op.f('setting_key'))
        batch_op.create_index(batch_op.f('ix_seo_settings_content_id'), ['content_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_seo_settings_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_seo_settings_is_active'), ['is_active'], unique=False)
        batch_op.create_index(batch_op.f('ix_seo_settings_page_type'), ['page_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_seo_settings_setting_key'), ['setting_key'], unique=True)
        batch_op.create_index(batch_op.f('ix_seo_settings_setting_type'), ['setting_type'], unique=False)
        batch_op.drop_table_comment(
        existing_comment='SEO配置表'
    )

    with op.batch_alter_table('site_settings', schema=None) as batch_op:
        batch_op.alter_column('setting_key',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=100),
               comment=None,
               existing_comment='设置键名',
               existing_nullable=False)
        batch_op.alter_column('setting_value',
               existing_type=mysql.JSON(),
               comment=None,
               existing_comment='设置值（JSON格式）',
               existing_nullable=True)
        batch_op.alter_column('setting_type',
               existing_type=mysql.ENUM('personal_info', 'theme', 'social_links', 'navigation', 'general', collation='utf8mb4_unicode_ci'),
               server_default=None,
               comment=None,
               existing_comment='设置类型',
               existing_nullable=True)
        batch_op.alter_column('description',
               existing_type=mysql.TEXT(collation='utf8mb4_unicode_ci'),
               comment=None,
               existing_comment='设置描述',
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.TIMESTAMP(),
               server_default=None,
               type_=sa.DateTime(),
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=mysql.TIMESTAMP(),
               server_default=None,
               type_=sa.DateTime(),
               existing_nullable=True)
        batch_op.drop_index(batch_op.f('idx_setting_key'))
        batch_op.drop_index(batch_op.f('idx_setting_type'))
        batch_op.drop_index(batch_op.f('setting_key'))
        batch_op.create_index(batch_op.f('ix_site_settings_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_site_settings_setting_key'), ['setting_key'], unique=True)
        batch_op.create_index(batch_op.f('ix_site_settings_setting_type'), ['setting_type'], unique=False)
        batch_op.drop_table_comment(
        existing_comment='站点配置表'
    )

    with op.batch_alter_table('system_configs', schema=None) as batch_op:
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('now()'),
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('now()'),
               existing_nullable=True)

    with op.batch_alter_table('system_metrics_history', schema=None) as batch_op:
        batch_op.alter_column('timestamp',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('now()'),
               existing_nullable=True)
        batch_op.alter_column('memory_total',
               existing_type=mysql.BIGINT(),
               type_=sa.Integer(),
               existing_nullable=True)
        batch_op.alter_column('memory_used',
               existing_type=mysql.BIGINT(),
               type_=sa.Integer(),
               existing_nullable=True)
        batch_op.alter_column('memory_available',
               existing_type=mysql.BIGINT(),
               type_=sa.Integer(),
               existing_nullable=True)
        batch_op.alter_column('disk_total',
               existing_type=mysql.BIGINT(),
               type_=sa.Integer(),
               existing_nullable=True)
        batch_op.alter_column('disk_used',
               existing_type=mysql.BIGINT(),
               type_=sa.Integer(),
               existing_nullable=True)
        batch_op.alter_column('disk_free',
               existing_type=mysql.BIGINT(),
               type_=sa.Integer(),
               existing_nullable=True)

    with op.batch_alter_table('tags', schema=None) as batch_op:
        batch_op.alter_column('color',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('icon',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=100),
               comment=None,
               existing_comment='标签图标，支持统一图标管理系统格式 (library:icon_key)',
               existing_nullable=True)
        batch_op.alter_column('category',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=None,
               existing_nullable=True)
        batch_op.drop_index(batch_op.f('idx_tags_category'))
        batch_op.drop_index(batch_op.f('idx_tags_category_name'))
        batch_op.drop_index(batch_op.f('idx_tags_icon'))
        batch_op.drop_index(batch_op.f('idx_tags_slug'))
        batch_op.create_index(batch_op.f('ix_tags_category'), ['category'], unique=False)
        batch_op.create_index(batch_op.f('ix_tags_icon'), ['icon'], unique=False)
        batch_op.create_index(batch_op.f('ix_tags_slug'), ['slug'], unique=True)

    with op.batch_alter_table('template_categories', schema=None) as batch_op:
        batch_op.alter_column('is_universal',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               existing_nullable=True)

    with op.batch_alter_table('theme_presets', schema=None) as batch_op:
        batch_op.alter_column('is_featured',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('is_active',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('usage_count',
               existing_type=mysql.INTEGER(),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=None,
               existing_nullable=True)
        batch_op.drop_index(batch_op.f('idx_category'))
        batch_op.drop_index(batch_op.f('idx_is_featured'))
        batch_op.drop_index(batch_op.f('idx_name'))
        batch_op.drop_index(batch_op.f('name'))
        batch_op.create_index(batch_op.f('ix_theme_presets_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_theme_presets_name'), ['name'], unique=True)

    with op.batch_alter_table('themes', schema=None) as batch_op:
        batch_op.alter_column('theme_type',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('is_active',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('is_default',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('is_public',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('version',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=20),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=None,
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=None,
               existing_nullable=True)
        batch_op.drop_index(batch_op.f('idx_is_active'))
        batch_op.drop_index(batch_op.f('idx_name'))
        batch_op.drop_index(batch_op.f('idx_theme_type'))
        batch_op.drop_index(batch_op.f('name'))
        batch_op.create_index(batch_op.f('ix_themes_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_themes_name'), ['name'], unique=True)

    # ### end Alembic commands ###


def downgrade() -> None:
    """降级数据库架构

    回滚upgrade()中的所有操作，包括：
    - 删除新创建的表
    - 移除新添加的字段
    - 恢复修改的字段
    - 回滚数据变更等

    注意：请确保回滚操作不会导致数据丢失
    """
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('themes', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_themes_name'))
        batch_op.drop_index(batch_op.f('ix_themes_id'))
        batch_op.create_index(batch_op.f('name'), ['name'], unique=True)
        batch_op.create_index(batch_op.f('idx_theme_type'), ['theme_type'], unique=False)
        batch_op.create_index(batch_op.f('idx_name'), ['name'], unique=False)
        batch_op.create_index(batch_op.f('idx_is_active'), ['is_active'], unique=False)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('CURRENT_TIMESTAMP'),
               existing_nullable=True)
        batch_op.alter_column('version',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=20),
               server_default=sa.text("'1.0'"),
               existing_nullable=True)
        batch_op.alter_column('is_public',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'1'"),
               existing_nullable=True)
        batch_op.alter_column('is_default',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'0'"),
               existing_nullable=True)
        batch_op.alter_column('is_active',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'0'"),
               existing_nullable=True)
        batch_op.alter_column('theme_type',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=sa.text("'custom'"),
               existing_nullable=True)

    with op.batch_alter_table('theme_presets', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_theme_presets_name'))
        batch_op.drop_index(batch_op.f('ix_theme_presets_id'))
        batch_op.create_index(batch_op.f('name'), ['name'], unique=True)
        batch_op.create_index(batch_op.f('idx_name'), ['name'], unique=False)
        batch_op.create_index(batch_op.f('idx_is_featured'), ['is_featured'], unique=False)
        batch_op.create_index(batch_op.f('idx_category'), ['category'], unique=False)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('CURRENT_TIMESTAMP'),
               existing_nullable=True)
        batch_op.alter_column('usage_count',
               existing_type=mysql.INTEGER(),
               server_default=sa.text("'0'"),
               existing_nullable=True)
        batch_op.alter_column('is_active',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'1'"),
               existing_nullable=True)
        batch_op.alter_column('is_featured',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'0'"),
               existing_nullable=True)

    with op.batch_alter_table('template_categories', schema=None) as batch_op:
        batch_op.alter_column('is_universal',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'0'"),
               existing_nullable=True)

    with op.batch_alter_table('tags', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_tags_slug'))
        batch_op.drop_index(batch_op.f('ix_tags_icon'))
        batch_op.drop_index(batch_op.f('ix_tags_category'))
        batch_op.create_index(batch_op.f('idx_tags_slug'), ['slug'], unique=True)
        batch_op.create_index(batch_op.f('idx_tags_icon'), ['icon'], unique=False)
        batch_op.create_index(batch_op.f('idx_tags_category_name'), ['category', 'name'], unique=False)
        batch_op.create_index(batch_op.f('idx_tags_category'), ['category'], unique=False)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
               existing_nullable=True)
        batch_op.alter_column('category',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=sa.text("'content'"),
               existing_nullable=True)
        batch_op.alter_column('icon',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=100),
               comment='标签图标，支持统一图标管理系统格式 (library:icon_key)',
               existing_nullable=True)
        batch_op.alter_column('color',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=sa.text("'blue'"),
               existing_nullable=True)

    with op.batch_alter_table('system_metrics_history', schema=None) as batch_op:
        batch_op.alter_column('disk_free',
               existing_type=sa.Integer(),
               type_=mysql.BIGINT(),
               existing_nullable=True)
        batch_op.alter_column('disk_used',
               existing_type=sa.Integer(),
               type_=mysql.BIGINT(),
               existing_nullable=True)
        batch_op.alter_column('disk_total',
               existing_type=sa.Integer(),
               type_=mysql.BIGINT(),
               existing_nullable=True)
        batch_op.alter_column('memory_available',
               existing_type=sa.Integer(),
               type_=mysql.BIGINT(),
               existing_nullable=True)
        batch_op.alter_column('memory_used',
               existing_type=sa.Integer(),
               type_=mysql.BIGINT(),
               existing_nullable=True)
        batch_op.alter_column('memory_total',
               existing_type=sa.Integer(),
               type_=mysql.BIGINT(),
               existing_nullable=True)
        batch_op.alter_column('timestamp',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('(now())'),
               existing_nullable=True)

    with op.batch_alter_table('system_configs', schema=None) as batch_op:
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('(now())'),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('(now())'),
               existing_nullable=True)

    with op.batch_alter_table('site_settings', schema=None) as batch_op:
        batch_op.create_table_comment(
        '站点配置表',
        existing_comment=None
    )
        batch_op.drop_index(batch_op.f('ix_site_settings_setting_type'))
        batch_op.drop_index(batch_op.f('ix_site_settings_setting_key'))
        batch_op.drop_index(batch_op.f('ix_site_settings_id'))
        batch_op.create_index(batch_op.f('setting_key'), ['setting_key'], unique=True)
        batch_op.create_index(batch_op.f('idx_setting_type'), ['setting_type'], unique=False)
        batch_op.create_index(batch_op.f('idx_setting_key'), ['setting_key'], unique=False)
        batch_op.alter_column('updated_at',
               existing_type=sa.DateTime(),
               server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
               type_=mysql.TIMESTAMP(),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=sa.DateTime(),
               server_default=sa.text('CURRENT_TIMESTAMP'),
               type_=mysql.TIMESTAMP(),
               existing_nullable=True)
        batch_op.alter_column('description',
               existing_type=mysql.TEXT(collation='utf8mb4_unicode_ci'),
               comment='设置描述',
               existing_nullable=True)
        batch_op.alter_column('setting_type',
               existing_type=mysql.ENUM('personal_info', 'theme', 'social_links', 'navigation', 'general', collation='utf8mb4_unicode_ci'),
               server_default=sa.text("'general'"),
               comment='设置类型',
               existing_nullable=True)
        batch_op.alter_column('setting_value',
               existing_type=mysql.JSON(),
               comment='设置值（JSON格式）',
               existing_nullable=True)
        batch_op.alter_column('setting_key',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=100),
               comment='设置键名',
               existing_nullable=False)

    with op.batch_alter_table('seo_settings', schema=None) as batch_op:
        batch_op.create_table_comment(
        'SEO配置表',
        existing_comment=None
    )
        batch_op.drop_index(batch_op.f('ix_seo_settings_setting_type'))
        batch_op.drop_index(batch_op.f('ix_seo_settings_setting_key'))
        batch_op.drop_index(batch_op.f('ix_seo_settings_page_type'))
        batch_op.drop_index(batch_op.f('ix_seo_settings_is_active'))
        batch_op.drop_index(batch_op.f('ix_seo_settings_id'))
        batch_op.drop_index(batch_op.f('ix_seo_settings_content_id'))
        batch_op.create_index(batch_op.f('setting_key'), ['setting_key'], unique=True)
        batch_op.create_index(batch_op.f('idx_setting_type'), ['setting_type'], unique=False)
        batch_op.create_index(batch_op.f('idx_setting_key'), ['setting_key'], unique=False)
        batch_op.create_index(batch_op.f('idx_page_type'), ['page_type'], unique=False)
        batch_op.create_index(batch_op.f('idx_is_active'), ['is_active'], unique=False)
        batch_op.create_index(batch_op.f('idx_content_id'), ['content_id'], unique=False)
        batch_op.alter_column('updated_at',
               existing_type=sa.DateTime(),
               server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
               type_=mysql.TIMESTAMP(),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=sa.DateTime(),
               server_default=sa.text('CURRENT_TIMESTAMP'),
               type_=mysql.TIMESTAMP(),
               existing_nullable=True)
        batch_op.alter_column('description',
               existing_type=mysql.TEXT(collation='utf8mb4_unicode_ci'),
               comment='SEO设置描述',
               existing_nullable=True)
        batch_op.alter_column('priority',
               existing_type=mysql.INTEGER(),
               server_default=sa.text("'0'"),
               nullable=True,
               comment='优先级（数字越大优先级越高）')
        batch_op.alter_column('is_active',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'1'"),
               nullable=True,
               comment='是否启用')
        batch_op.alter_column('content_id',
               existing_type=mysql.INTEGER(),
               comment='内容ID（用于内容级别的SEO设置）',
               existing_nullable=True)
        batch_op.alter_column('page_type',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               comment='页面类型（homepage, blog, project, about等）',
               existing_nullable=True)
        batch_op.alter_column('setting_type',
               existing_type=mysql.ENUM('global', 'page', 'content', 'social', 'analytics', collation='utf8mb4_unicode_ci'),
               server_default=sa.text("'global'"),
               comment='SEO设置类型',
               existing_nullable=True)
        batch_op.alter_column('setting_value',
               existing_type=mysql.JSON(),
               comment='SEO设置值（JSON格式）',
               existing_nullable=True)
        batch_op.alter_column('setting_key',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=100),
               comment='SEO设置键名',
               existing_nullable=False)

    with op.batch_alter_table('page_layouts', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_page_layouts_page_type'))
        batch_op.drop_index(batch_op.f('ix_page_layouts_id'))
        batch_op.create_index(batch_op.f('page_type'), ['page_type'], unique=True)
        batch_op.create_index(batch_op.f('idx_page_type'), ['page_type'], unique=False)
        batch_op.create_index(batch_op.f('idx_is_active'), ['is_active'], unique=False)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('CURRENT_TIMESTAMP'),
               existing_nullable=True)
        batch_op.alter_column('version',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=20),
               server_default=sa.text("'1.0'"),
               existing_nullable=True)
        batch_op.alter_column('is_active',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'1'"),
               existing_nullable=True)

    with op.batch_alter_table('navigation_items', schema=None) as batch_op:
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('(now())'),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('(now())'),
               existing_nullable=True)

    with op.batch_alter_table('layout_blocks', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_layout_blocks_id'))
        batch_op.drop_index(batch_op.f('ix_layout_blocks_block_id'))
        batch_op.create_index(batch_op.f('idx_page_type'), ['page_type'], unique=False)
        batch_op.create_index(batch_op.f('idx_display_order'), ['display_order'], unique=False)
        batch_op.create_index(batch_op.f('idx_block_id'), ['block_id'], unique=False)
        batch_op.create_index(batch_op.f('block_id'), ['block_id'], unique=True)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('CURRENT_TIMESTAMP'),
               existing_nullable=True)
        batch_op.alter_column('page_type',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=sa.text("'homepage'"),
               existing_nullable=True)
        batch_op.alter_column('display_order',
               existing_type=mysql.INTEGER(),
               server_default=sa.text("'0'"),
               existing_nullable=True)
        batch_op.alter_column('enabled',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'1'"),
               existing_nullable=True)

    with op.batch_alter_table('images', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_index(batch_op.f('idx_images_date_created'), ['date', 'created_at'], unique=False)
        batch_op.create_index(batch_op.f('idx_display_name'), ['display_name'], unique=False)
        batch_op.drop_column('external_id')
        batch_op.drop_column('upload_source')
        batch_op.drop_column('category_id')

    with op.batch_alter_table('educations', schema=None) as batch_op:
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('(now())'),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('(now())'),
               existing_nullable=True)

    with op.batch_alter_table('content_templates', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key(batch_op.f('content_templates_ibfk_1'), 'users', ['created_by'], ['id'], ondelete='SET NULL')
        batch_op.drop_index(batch_op.f('ix_content_templates_id'))
        batch_op.create_index(batch_op.f('idx_is_active'), ['is_active'], unique=False)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('CURRENT_TIMESTAMP'),
               existing_nullable=True)
        batch_op.alter_column('usage_count',
               existing_type=mysql.INTEGER(),
               server_default=sa.text("'0'"),
               existing_nullable=True)
        batch_op.alter_column('is_system',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'0'"),
               existing_nullable=True)
        batch_op.alter_column('is_default',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'0'"),
               existing_nullable=True)
        batch_op.alter_column('is_active',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'1'"),
               existing_nullable=True)
        batch_op.alter_column('template_type',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=sa.text("'blog'"),
               existing_nullable=True)

    with op.batch_alter_table('content_drafts', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key(batch_op.f('content_drafts_ibfk_2'), 'content_drafts', ['parent_draft_id'], ['id'], ondelete='SET NULL')
        batch_op.create_foreign_key(batch_op.f('content_drafts_ibfk_1'), 'content_templates', ['template_id'], ['id'], ondelete='SET NULL')
        batch_op.create_foreign_key(batch_op.f('content_drafts_ibfk_3'), 'users', ['created_by'], ['id'], ondelete='CASCADE')
        batch_op.drop_index(batch_op.f('ix_content_drafts_id'))
        batch_op.create_index(batch_op.f('idx_status'), ['status'], unique=False)
        batch_op.create_index(batch_op.f('idx_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('idx_content_type'), ['content_type'], unique=False)
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('CURRENT_TIMESTAMP'),
               existing_nullable=True)
        batch_op.alter_column('auto_save_interval',
               existing_type=mysql.INTEGER(),
               server_default=sa.text("'30'"),
               existing_nullable=True)
        batch_op.alter_column('version',
               existing_type=mysql.INTEGER(),
               server_default=sa.text("'1'"),
               existing_nullable=True)
        batch_op.alter_column('status',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=20),
               server_default=sa.text("'draft'"),
               existing_nullable=True)

    with op.batch_alter_table('careers', schema=None) as batch_op:
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('(now())'),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('(now())'),
               existing_nullable=True)

    with op.batch_alter_table('blogs', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_blogs_display_date'))
        batch_op.drop_index(batch_op.f('ix_blogs_article_type'))
        batch_op.create_index(batch_op.f('ix_blogs_date'), ['display_date'], unique=False)
        batch_op.create_index(batch_op.f('idx_is_open_source'), ['is_open_source'], unique=False)
        batch_op.create_index(batch_op.f('idx_featured'), ['featured'], unique=False)
        batch_op.create_index(batch_op.f('idx_blogs_type_published_homepage'), ['article_type', 'published', 'show_on_homepage'], unique=False)
        batch_op.create_index(batch_op.f('idx_blogs_homepage_order'), ['homepage_order', 'show_on_homepage'], unique=False)
        batch_op.create_index(batch_op.f('idx_blogs_homepage_display'), ['show_on_homepage', 'published', 'article_type', 'homepage_order', 'display_date'], unique=False)
        batch_op.create_index(batch_op.f('idx_blogs_featured_published'), ['featured', 'published'], unique=False)
        batch_op.create_index(batch_op.f('idx_blogs_display_date_published'), ['display_date', 'published'], unique=False)
        batch_op.create_index(batch_op.f('idx_blogs_article_type_published'), ['article_type', 'published'], unique=False)
        batch_op.create_index(batch_op.f('idx_blogs_article_type'), ['article_type'], unique=False)
        batch_op.create_index(batch_op.f('idx_article_type_open_source'), ['article_type', 'is_open_source'], unique=False)
        batch_op.create_index(batch_op.f('idx_article_type_featured'), ['article_type', 'featured'], unique=False)
        batch_op.create_index(batch_op.f('ft_blogs_title_description'), ['title', 'description'], unique=False, mysql_prefix='FULLTEXT')
        batch_op.alter_column('published_at',
               existing_type=mysql.DATETIME(),
               comment='发布时间',
               existing_nullable=True)
        batch_op.alter_column('homepage_order',
               existing_type=mysql.INTEGER(),
               server_default=sa.text("'0'"),
               comment='首页显示顺序',
               existing_nullable=True)
        batch_op.alter_column('display_order',
               existing_type=mysql.INTEGER(),
               server_default=sa.text("'0'"),
               existing_nullable=True)
        batch_op.alter_column('featured',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'0'"),
               existing_nullable=True)
        batch_op.alter_column('is_open_source',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'0'"),
               comment='是否开源项目',
               existing_nullable=True)
        batch_op.alter_column('is_github_project',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'0'"),
               existing_nullable=True)
        batch_op.alter_column('article_type',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=sa.text("'blog'"),
               existing_nullable=False)
        batch_op.alter_column('display_date',
               existing_type=mysql.DATETIME(),
               comment='展示日期，用于前端显示和排序',
               existing_nullable=False)

    with op.batch_alter_table('blog_versions', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('idx_blog_versions_is_major_change'), ['is_major_change'], unique=False)
        batch_op.alter_column('is_major_change',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'0'"),
               nullable=False)
        batch_op.alter_column('display_date',
               existing_type=mysql.DATETIME(),
               comment='展示日期',
               existing_nullable=False)

    with op.batch_alter_table('api_metrics', schema=None) as batch_op:
        batch_op.alter_column('timestamp',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('(now())'),
               existing_nullable=True)

    with op.batch_alter_table('albums', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_public', mysql.TINYINT(display_width=1), server_default=sa.text("'1'"), autoincrement=False, nullable=True))
        batch_op.drop_constraint(None, type_='unique')
        batch_op.create_index(batch_op.f('idx_albums_sort_order'), ['sort_order'], unique=False)
        batch_op.create_index(batch_op.f('idx_albums_slug'), ['slug'], unique=False)
        batch_op.create_index(batch_op.f('idx_albums_public_featured'), ['is_public', 'is_featured'], unique=False)
        batch_op.create_index(batch_op.f('idx_albums_is_public'), ['is_public'], unique=False)
        batch_op.create_index(batch_op.f('idx_albums_is_featured'), ['is_featured'], unique=False)
        batch_op.create_index(batch_op.f('idx_albums_category'), ['category'], unique=False)
        batch_op.alter_column('sort_order',
               existing_type=mysql.INTEGER(),
               server_default=sa.text("'0'"),
               comment=None,
               existing_comment='排序顺序',
               existing_nullable=True)
        batch_op.alter_column('is_featured',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'0'"),
               comment=None,
               existing_comment='是否精选',
               existing_nullable=True)
        batch_op.alter_column('slug',
               existing_type=sa.String(length=100),
               type_=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=255),
               comment=None,
               existing_comment='URL友好名称',
               existing_nullable=True)
        batch_op.alter_column('category',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=100),
               comment=None,
               existing_comment='相册分类',
               existing_nullable=True)
        batch_op.drop_column('layout_type')
        batch_op.drop_column('type')

    with op.batch_alter_table('about_pages', schema=None) as batch_op:
        batch_op.alter_column('updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('(now())'),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('(now())'),
               existing_nullable=True)
        batch_op.alter_column('photo_size',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=sa.text("'medium'"),
               existing_nullable=True)
        batch_op.alter_column('photo_style',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=sa.text("'rounded'"),
               existing_nullable=True)
        batch_op.alter_column('photo_position',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               server_default=sa.text("'right'"),
               existing_nullable=True)

    op.create_table('icon_favorites',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('icon_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('user_id', mysql.INTEGER(), autoincrement=False, nullable=True, comment='用户ID'),
    sa.Column('session_id', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=100), nullable=True, comment='会话ID'),
    sa.Column('created_at', mysql.TIMESTAMP(), nullable=False),
    sa.ForeignKeyConstraint(['icon_id'], ['icon_metadata.id'], name=op.f('icon_favorites_ibfk_1'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_unicode_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    with op.batch_alter_table('icon_favorites', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('uq_icon_user_favorite'), ['icon_id', 'user_id'], unique=True)
        batch_op.create_index(batch_op.f('uq_icon_session_favorite'), ['icon_id', 'session_id'], unique=True)
        batch_op.create_index(batch_op.f('ix_icon_favorites_id'), ['id'], unique=False)

    # ### end Alembic commands ###
