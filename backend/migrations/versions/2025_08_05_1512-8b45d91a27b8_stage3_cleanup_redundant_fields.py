"""stage3_cleanup_redundant_fields

Revision ID: 8b45d91a27b8
Revises: cb351124baaa
Create Date: 2025-08-05 15:12:53.373996+08:00

Portfolio项目数据库迁移文件
此文件由Alembic自动生成，请仔细审查后再应用到生产环境。

变更说明:
- 请在此处详细描述本次迁移的具体变更内容
- 包括新增/修改/删除的表、字段、索引等
- 说明变更的业务背景和影响范围

注意事项:
- 在生产环境应用前请务必备份数据库
- 复杂的数据迁移建议分步执行
- 确保回滚操作的正确性和安全性
"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8b45d91a27b8'
down_revision: Union[str, None] = 'cb351124baaa'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """第三阶段清理：删除冗余字段

    本阶段删除以下冗余字段：
    1. blogs表中的冗余字段：
       - has_detail_page（所有文章都有详情页）
       - tech_stack（可通过标签系统管理）
    2. albums表中的冗余字段：
       - type（如果不使用类型分类）
       - layout_type（如果使用统一布局）
    3. images表中的冗余字段：
       - upload_source（如果只支持本地上传）
       - external_id（如果不使用外部服务）

    这些字段要么不再使用，要么功能重复，删除后可简化数据模型。
    """

    # 删除blogs表中的冗余字段
    try:
        op.drop_column('blogs', 'has_detail_page')
    except Exception:
        pass  # 如果字段不存在，忽略错误

    try:
        op.drop_column('blogs', 'tech_stack')
    except Exception:
        pass  # 如果字段不存在，忽略错误

    # 删除albums表中的冗余字段
    try:
        op.drop_column('albums', 'type')
    except Exception:
        pass  # 如果字段不存在，忽略错误

    try:
        op.drop_column('albums', 'layout_type')
    except Exception:
        pass  # 如果字段不存在，忽略错误

    # 删除images表中的冗余字段
    try:
        op.drop_column('images', 'upload_source')
    except Exception:
        pass  # 如果字段不存在，忽略错误

    try:
        op.drop_column('images', 'external_id')
    except Exception:
        pass  # 如果字段不存在，忽略错误


def downgrade() -> None:
    """第三阶段清理回滚

    重新添加已删除的冗余字段。
    注意：字段数据将丢失，建议从备份恢复。
    """

    # 重新添加blogs表字段
    op.add_column('blogs', sa.Column('has_detail_page', sa.Boolean(), nullable=True, default=True))
    op.add_column('blogs', sa.Column('tech_stack', sa.JSON(), nullable=True))

    # 重新添加albums表字段
    op.add_column('albums', sa.Column('type', sa.Enum('year','location','theme','category'), nullable=True))
    op.add_column('albums', sa.Column('layout_type', sa.String(50), nullable=True))

    # 重新添加images表字段
    op.add_column('images', sa.Column('upload_source', sa.Enum('local','url','image_bed'), nullable=True))
    op.add_column('images', sa.Column('external_id', sa.String(255), nullable=True))
