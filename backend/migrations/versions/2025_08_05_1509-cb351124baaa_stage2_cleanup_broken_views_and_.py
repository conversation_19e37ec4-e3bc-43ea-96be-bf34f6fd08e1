"""stage2_cleanup_broken_views_and_duplicates

Revision ID: cb351124baaa
Revises: 9639b138f536
Create Date: 2025-08-05 15:09:37.007292+08:00

Portfolio项目数据库迁移文件
此文件由Alembic自动生成，请仔细审查后再应用到生产环境。

变更说明:
- 请在此处详细描述本次迁移的具体变更内容
- 包括新增/修改/删除的表、字段、索引等
- 说明变更的业务背景和影响范围

注意事项:
- 在生产环境应用前请务必备份数据库
- 复杂的数据迁移建议分步执行
- 确保回滚操作的正确性和安全性
"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cb351124baaa'
down_revision: Union[str, None] = '9639b138f536'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """第二阶段清理：删除重复表

    本阶段删除以下问题表：
    1. template_category_associations - 与template_category_association功能重复
    2. image_categories - 如果使用标签系统替代分类系统（需先删除外键约束）
    3. system_configs - 与site_settings功能重复的配置表

    注意：gallery_stats视图由于权限问题跳过，需要手动处理
    """

    # 删除重复的关联表（如果存在）
    op.execute("DROP TABLE IF EXISTS template_category_associations")

    # 删除images表中对image_categories的外键约束
    try:
        op.execute("ALTER TABLE images DROP FOREIGN KEY fk_images_category_id")
    except Exception:
        pass  # 如果外键不存在，忽略错误

    try:
        op.execute("ALTER TABLE images DROP INDEX idx_category")
    except Exception:
        pass  # 如果索引不存在，忽略错误

    # 删除images表中的category_id字段
    op.drop_column('images', 'category_id')

    # 删除图片分类表（现在可以安全删除）
    op.drop_table('image_categories')

    # 删除重复的系统配置表（与site_settings功能重复）
    op.drop_table('system_configs')


def downgrade() -> None:
    """第二阶段清理回滚

    警告：此回滚操作会重新创建已删除的表和视图，但数据将丢失！
    建议从备份恢复而不是使用此回滚操作。
    """

    # 重新创建system_configs表
    op.create_table('system_configs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('key', sa.String(255), nullable=False),
        sa.Column('value', sa.Text(), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.Index('ix_system_configs_key', 'key')
    )

    # 重新创建image_categories表
    op.create_table('image_categories',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(100), nullable=False),
        sa.Column('slug', sa.String(100), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('color', sa.String(50), nullable=True),
        sa.Column('icon', sa.String(100), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # 重新创建template_category_associations表（如果需要）
    op.create_table('template_category_associations',
        sa.Column('template_id', sa.Integer(), nullable=False),
        sa.Column('category_id', sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint('template_id', 'category_id')
    )

    # 注意：gallery_stats视图由于损坏，无法在此重新创建
    # 如需恢复，请从备份中手动恢复
