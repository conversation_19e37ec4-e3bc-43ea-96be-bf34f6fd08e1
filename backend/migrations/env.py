"""
Portfolio项目数据库迁移环境配置
支持开发环境和生产环境的自动切换

功能特性:
- 自动检测和导入所有模型
- 支持MySQL数据库连接
- 开发/生产环境自适应
- 完整的错误处理和日志记录
- 支持字段类型和默认值比较
"""
from logging.config import fileConfig
import os
import sys
from urllib.parse import quote_plus

from sqlalchemy import engine_from_config, pool, create_engine
from alembic import context

# 添加项目根目录到Python路径
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

# 导入配置和模型
try:
    from app.config import settings
    from database.database import Base

    # 导入所有模型以注册到Base.metadata
    # 这确保alembic能够检测到所有表结构变化
    import models.about_page
    import models.album
    import models.blog
    import models.blog_version
    import models.content_template
    import models.gallery_timeline
    import models.icon_models
    import models.image
    import models.layout_block
    import models.personal_info
    import models.seo_settings
    import models.site_settings
    import models.system_config
    import models.system_metrics
    import models.tag
    import models.template_category
    import models.theme
    import models.user
    import models.website_version

    print("✓ 所有模型已成功导入")

except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保:")
    print("1. 在backend目录下运行alembic命令")
    print("2. 虚拟环境已激活 (poetry shell)")
    print("3. 所有依赖已安装 (poetry install)")
    print("4. 数据库配置正确")
    sys.exit(1)

# Alembic配置对象
config = context.config

# 设置日志配置
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# 目标元数据 - 用于自动生成迁移
target_metadata = Base.metadata

# 构建数据库连接URL
def get_database_url():
    """根据环境变量构建数据库连接URL

    Returns:
        str: 完整的数据库连接URL

    Raises:
        Exception: 当数据库配置无效时抛出异常
    """
    try:
        # 验证必需的配置项
        required_configs = ['DB_USER', 'DB_PASSWORD', 'DB_HOST', 'DB_PORT', 'DB_NAME']
        missing_configs = []

        for config in required_configs:
            if not hasattr(settings, config) or not getattr(settings, config):
                missing_configs.append(config)

        if missing_configs:
            raise ValueError(f"缺少必需的数据库配置: {', '.join(missing_configs)}")

        # URL编码密码以处理特殊字符
        encoded_password = quote_plus(settings.DB_PASSWORD)
        database_url = f"mysql+pymysql://{settings.DB_USER}:{encoded_password}@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}"

        # 在开发环境下显示连接信息（隐藏密码）
        if getattr(settings, 'DEBUG', False):
            safe_url = f"mysql+pymysql://{settings.DB_USER}:***@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}"
            print(f"✓ 数据库连接: {safe_url}")

        return database_url
    except Exception as e:
        print(f"❌ 构建数据库URL失败: {e}")
        print("请检查数据库配置是否正确")
        raise

DATABASE_URL = get_database_url()

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """离线模式运行迁移

    在此模式下，只需要URL而不需要Engine，
    适用于生成SQL脚本而不直接执行的场景。

    特性:
    - 生成SQL脚本而不执行
    - 适用于生产环境的预审查
    - 支持类型和默认值比较
    """
    print("🔄 运行离线模式迁移...")

    context.configure(
        url=DATABASE_URL,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,  # 比较字段类型变化
        compare_server_default=True,  # 比较默认值变化
        render_as_batch=True,  # 支持SQLite等数据库的批量操作
    )

    with context.begin_transaction():
        context.run_migrations()
        print("✓ 离线模式迁移完成")


def run_migrations_online() -> None:
    """在线模式运行迁移

    创建Engine并与context关联，直接执行迁移。

    特性:
    - 直接连接数据库执行迁移
    - 支持事务回滚
    - 完整的错误处理
    - 调试模式下显示SQL语句
    """
    print("🔄 运行在线模式迁移...")

    # 创建数据库引擎
    connectable = create_engine(
        DATABASE_URL,
        poolclass=pool.NullPool,  # 不使用连接池，避免连接泄露
        echo=getattr(settings, 'DEBUG', False),  # 在调试模式下显示SQL
        pool_pre_ping=True,  # 连接前检查连接有效性
        connect_args={
            "charset": "utf8mb4",  # 支持完整的UTF-8字符集
            "autocommit": False,   # 禁用自动提交
        }
    )

    try:
        with connectable.connect() as connection:
            context.configure(
                connection=connection,
                target_metadata=target_metadata,
                compare_type=True,  # 比较字段类型变化
                compare_server_default=True,  # 比较默认值变化
                render_as_batch=True,  # 支持批量操作
                transaction_per_migration=True,  # 每个迁移使用独立事务
            )

            with context.begin_transaction():
                context.run_migrations()
                print("✓ 在线模式迁移完成")

    except Exception as e:
        print(f"❌ 迁移执行失败: {e}")
        raise
    finally:
        connectable.dispose()


# 根据运行模式选择迁移方式
print(f"🚀 启动Alembic迁移系统...")
print(f"📊 目标元数据包含 {len(target_metadata.tables)} 个表")

if context.is_offline_mode():
    print("📝 使用离线模式（生成SQL脚本）")
    run_migrations_offline()
else:
    print("🔗 使用在线模式（直接执行迁移）")
    run_migrations_online()

print("🎉 Alembic迁移系统执行完成")
