2025-07-21 09:04:54 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-07-21 09:04:54 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_categories_id' on 'project_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_categories_name' on 'project_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_categories_slug' on 'project_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'project_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_icon_favorites_id' on 'icon_favorites'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_session_favorite' on 'icon_favorites'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_user_favorite' on 'icon_favorites'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'icon_favorites'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_icon_favorites_old_id' on 'icon_favorites_old'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_session_favorite_old' on 'icon_favorites_old'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_user_favorite_old' on 'icon_favorites_old'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'icon_favorites_old'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_projects_id' on 'projects'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'projects'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'project_categories_association'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'project_module_categories_association'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_module_categories_id' on 'project_module_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_module_categories_slug' on 'project_module_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'project_module_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'project_module_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_position'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_style'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_size'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_category' on 'albums'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_is_featured' on 'albums'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_is_public' on 'albums'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_slug' on 'albums'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_sort_order' on 'albums'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed column 'albums.is_featured'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed column 'albums.is_public'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed column 'albums.category'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed column 'albums.slug'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed column 'albums.sort_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'api_metrics.timestamp'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'blog_versions.display_date'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected NULL on column 'blog_versions.is_major_change'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'blog_versions.is_major_change'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blog_versions_is_major_change' on 'blog_versions'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.display_date'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.article_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.is_github_project'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.is_open_source'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.is_open_source'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.featured'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.display_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.homepage_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.homepage_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.published_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ft_blogs_title_description' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_article_type_featured' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_article_type_open_source' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_article_type' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_homepage_display' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_homepage_order' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_type_published_homepage' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_featured' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_open_source' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_blogs_date' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_blogs_article_type' on '('article_type',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_blogs_display_date' on '('display_date',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed column 'blogs.has_detail_page'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed column 'blogs.tech_stack'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'careers.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'careers.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.status'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.version'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.auto_save_interval'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_content_type' on 'content_drafts'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_created_by' on 'content_drafts'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_status' on 'content_drafts'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_content_drafts_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (created_by)(id) on table content_drafts
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (parent_draft_id)(id) on table content_drafts
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (template_id)(id) on table content_drafts
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (created_by)(id) on table content_drafts
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (parent_draft_id)(id) on table content_drafts
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (template_id)(id) on table content_drafts
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.template_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_default'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_system'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.usage_count'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'content_templates'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_content_templates_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (created_by)(id) on table content_templates
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (created_by)(id) on table content_templates
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'educations.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'educations.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_categories.sort_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_categories.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_categories.layout_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_categories.columns'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_categories.image_ratio'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_categories.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_categories.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_categories_is_active' on 'gallery_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_categories_slug' on 'gallery_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_categories_sort_order' on 'gallery_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_gallery_categories_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (cover_image_id)(id) on table gallery_categories
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (cover_image_id)(id) on table gallery_categories
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_collection_items.sort_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_collection_items.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_collection_items_collection_id' on 'gallery_collection_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_collection_items_sort_order' on 'gallery_collection_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'uk_gallery_collection_items' on 'gallery_collection_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_gallery_collection_items_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (collection_id)(id) on table gallery_collection_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (gallery_item_id)(id) on table gallery_collection_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (collection_id)(id) on table gallery_collection_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (gallery_item_id)(id) on table gallery_collection_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_collections.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_collections.is_featured'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_collections.sort_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_collections.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_collections.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_collections_is_active' on 'gallery_collections'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_collections_is_featured' on 'gallery_collections'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_collections_slug' on 'gallery_collections'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_collections_sort_order' on 'gallery_collections'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_gallery_collections_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (cover_image_id)(id) on table gallery_collections
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (cover_image_id)(id) on table gallery_collections
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_items.sort_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_items.is_featured'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_items.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_items.view_count'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_items.like_count'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_items.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_items.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_items_category_id' on 'gallery_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_items_is_active' on 'gallery_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_items_is_featured' on 'gallery_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_items_sort_order' on 'gallery_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'uk_gallery_items_image_category' on 'gallery_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_gallery_items_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (image_id)(id) on table gallery_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (category_id)(id) on table gallery_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (image_id)(id) on table gallery_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (category_id)(id) on table gallery_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_timeline_entries.show_on_homepage'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_category' on 'images'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_display_name' on 'images'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.enabled'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.display_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.page_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'block_id' on 'layout_blocks'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_block_id' on 'layout_blocks'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_display_order' on 'layout_blocks'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'layout_blocks'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_layout_blocks_block_id' on '('block_id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_layout_blocks_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'navigation_items.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'navigation_items.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.version'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'page_layouts'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'page_layouts'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'page_type' on 'page_layouts'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_page_layouts_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_page_layouts_page_type' on '('page_type',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_key'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_value'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.setting_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.page_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.content_id'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected NOT NULL on column 'seo_settings.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected NOT NULL on column 'seo_settings.priority'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.priority'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.priority'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.description'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'seo_settings.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'seo_settings.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_content_id' on 'seo_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'seo_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'seo_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_key' on 'seo_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_type' on 'seo_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'setting_key' on 'seo_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_content_id' on '('content_id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_is_active' on '('is_active',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_page_type' on '('page_type',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_setting_key' on '('setting_key',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_setting_type' on '('setting_type',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_key'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_value'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.setting_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.description'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'site_settings.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'site_settings.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_key' on 'site_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_type' on 'site_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'setting_key' on 'site_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_setting_key' on '('setting_key',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_setting_type' on '('setting_type',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'system_metrics_history.timestamp'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.color'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'tags.icon'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.category'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_category' on 'tags'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_icon' on 'tags'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_slug' on 'tags'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_category' on '('category',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_icon' on '('icon',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_slug' on '('slug',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'template_categories.is_universal'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.is_featured'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.usage_count'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_category' on 'theme_presets'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_featured' on 'theme_presets'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_name' on 'theme_presets'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'theme_presets'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_theme_presets_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_theme_presets_name' on '('name',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.theme_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_default'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_public'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.version'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'themes'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_name' on 'themes'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_theme_type' on 'themes'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'themes'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_themes_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_themes_name' on '('name',)'
2025-07-21 09:05:04 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-07-21 09:05:04 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-07-21 09:05:04 INFO  [alembic.runtime.migration] Running upgrade remove_usage_type_and_is_public_fields -> 9d0ec1bbd459, Add system metrics history tables
2025-07-21 09:06:18 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-07-21 09:06:18 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-07-21 09:06:18 INFO  [alembic.runtime.migration] Running upgrade remove_usage_type_and_is_public_fields -> 9d0ec1bbd459, Add system metrics history tables
2025-08-05 14:49:06 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 14:49:06 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 14:49:06 ERROR [alembic.util.messaging] Can't locate revision identified by 'remove_usage_type_and_is_public_fields'
2025-08-05 14:49:33 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 14:49:33 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 14:49:33 ERROR [alembic.util.messaging] Can't locate revision identified by 'remove_usage_type_and_is_public_fields'
2025-08-05 14:49:52 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 14:49:52 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed table 'project_categories'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed table 'project_categories_association'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed table 'categories'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_icon_favorites_old_id' on 'icon_favorites_old'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_session_favorite_old' on 'icon_favorites_old'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_user_favorite_old' on 'icon_favorites_old'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed table 'icon_favorites_old'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_module_categories_id' on 'project_module_categories'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_module_categories_slug' on 'project_module_categories'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'project_module_categories'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed table 'project_module_categories'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_projects_id' on 'projects'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed table 'projects'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_icon_favorites_id' on 'icon_favorites'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_session_favorite' on 'icon_favorites'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_user_favorite' on 'icon_favorites'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed table 'icon_favorites'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed table 'project_module_categories_association'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_system_configs_id' on 'system_configs'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_system_configs_key' on 'system_configs'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected removed table 'system_configs'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_position'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_style'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_size'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.created_at'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.updated_at'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.type'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.category'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected type change from VARCHAR(collation='utf8mb4_unicode_ci', length=255) to String(length=100) on 'albums.slug'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.slug'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.layout_type'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.layout_type'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.is_featured'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.is_featured'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.sort_order'
2025-08-05 14:49:52 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.sort_order'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_category' on 'albums'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_is_featured' on 'albums'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_is_public' on 'albums'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_slug' on 'albums'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_sort_order' on 'albums'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added unique constraint None on '('slug',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed column 'albums.is_public'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'api_metrics.timestamp'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'blog_versions.display_date'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected NULL on column 'blog_versions.is_major_change'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'blog_versions.is_major_change'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blog_versions_is_major_change' on 'blog_versions'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.display_date'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.article_type'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.is_github_project'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.is_open_source'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.is_open_source'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.featured'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.display_order'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.homepage_order'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.homepage_order'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.published_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'ft_blogs_title_description' on 'blogs'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_article_type_featured' on 'blogs'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_article_type_open_source' on 'blogs'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_article_type' on 'blogs'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_homepage_display' on 'blogs'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_homepage_order' on 'blogs'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_type_published_homepage' on 'blogs'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_featured' on 'blogs'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_open_source' on 'blogs'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_blogs_date' on 'blogs'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_blogs_article_type' on '('article_type',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_blogs_display_date' on '('display_date',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed column 'blogs.tech_stack'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed column 'blogs.has_detail_page'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'careers.created_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'careers.updated_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.status'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.version'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.auto_save_interval'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.created_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.updated_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_content_type' on 'content_drafts'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_created_by' on 'content_drafts'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_status' on 'content_drafts'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_content_drafts_id' on '('id',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed foreign key (template_id)(id) on table content_drafts
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed foreign key (parent_draft_id)(id) on table content_drafts
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed foreign key (created_by)(id) on table content_drafts
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added foreign key (created_by)(id) on table content_drafts
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added foreign key (template_id)(id) on table content_drafts
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added foreign key (parent_draft_id)(id) on table content_drafts
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.template_type'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_active'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_default'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_system'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.usage_count'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.created_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.updated_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'content_templates'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_content_templates_id' on '('id',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed foreign key (created_by)(id) on table content_templates
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added foreign key (created_by)(id) on table content_templates
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'educations.created_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'educations.updated_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_category' on 'images'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_display_name' on 'images'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.enabled'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.display_order'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.page_type'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.created_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.updated_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'block_id' on 'layout_blocks'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_block_id' on 'layout_blocks'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_display_order' on 'layout_blocks'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'layout_blocks'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_layout_blocks_block_id' on '('block_id',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_layout_blocks_id' on '('id',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'navigation_items.created_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'navigation_items.updated_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.is_active'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.version'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.created_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.updated_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'page_layouts'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'page_layouts'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'page_type' on 'page_layouts'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_page_layouts_id' on '('id',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_page_layouts_page_type' on '('page_type',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_key'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_value'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.setting_type'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_type'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.page_type'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.content_id'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected NOT NULL on column 'seo_settings.is_active'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.is_active'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.is_active'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected NOT NULL on column 'seo_settings.priority'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.priority'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.priority'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.description'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'seo_settings.created_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.created_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'seo_settings.updated_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.updated_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_content_id' on 'seo_settings'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'seo_settings'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'seo_settings'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_key' on 'seo_settings'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_type' on 'seo_settings'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'setting_key' on 'seo_settings'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_content_id' on '('content_id',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_id' on '('id',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_is_active' on '('is_active',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_page_type' on '('page_type',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_setting_key' on '('setting_key',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_setting_type' on '('setting_type',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_key'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_value'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.setting_type'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_type'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.description'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'site_settings.created_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.created_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'site_settings.updated_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.updated_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_key' on 'site_settings'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_type' on 'site_settings'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'setting_key' on 'site_settings'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_id' on '('id',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_setting_key' on '('setting_key',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_setting_type' on '('setting_type',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'system_metrics_history.timestamp'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.memory_total'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.memory_used'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.memory_available'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.disk_total'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.disk_used'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.disk_free'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.color'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected column comment 'tags.icon'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.category'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.updated_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_category' on 'tags'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_icon' on 'tags'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_slug' on 'tags'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_category' on '('category',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_icon' on '('icon',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_slug' on '('slug',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'template_categories.is_universal'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.is_featured'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.is_active'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.usage_count'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.created_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.updated_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_category' on 'theme_presets'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_featured' on 'theme_presets'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_name' on 'theme_presets'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'theme_presets'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_theme_presets_id' on '('id',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_theme_presets_name' on '('name',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.theme_type'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_active'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_default'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_public'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.version'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.created_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.updated_at'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'themes'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_name' on 'themes'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_theme_type' on 'themes'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'themes'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_themes_id' on '('id',)'
2025-08-05 14:49:53 INFO  [alembic.autogenerate.compare] Detected added index 'ix_themes_name' on '('name',)'
2025-08-05 14:50:05 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 14:50:05 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 14:50:05 INFO  [alembic.runtime.migration] Running stamp_revision  -> c9c1c6a1dc4e
2025-08-05 14:50:13 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 14:50:13 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 14:50:33 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 14:50:33 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed table 'project_categories_association'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_module_categories_id' on 'project_module_categories'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_module_categories_slug' on 'project_module_categories'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'project_module_categories'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed table 'project_module_categories'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed table 'project_module_categories_association'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed table 'project_categories'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed table 'categories'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_system_configs_id' on 'system_configs'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_system_configs_key' on 'system_configs'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed table 'system_configs'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_icon_favorites_old_id' on 'icon_favorites_old'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_session_favorite_old' on 'icon_favorites_old'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_user_favorite_old' on 'icon_favorites_old'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed table 'icon_favorites_old'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_icon_favorites_id' on 'icon_favorites'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_session_favorite' on 'icon_favorites'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_user_favorite' on 'icon_favorites'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed table 'icon_favorites'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_projects_id' on 'projects'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed table 'projects'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_position'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_style'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_size'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.created_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.updated_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.type'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.category'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected type change from VARCHAR(collation='utf8mb4_unicode_ci', length=255) to String(length=100) on 'albums.slug'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.slug'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.layout_type'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.layout_type'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.is_featured'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.is_featured'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.sort_order'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.sort_order'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_category' on 'albums'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_is_featured' on 'albums'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_is_public' on 'albums'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_slug' on 'albums'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_sort_order' on 'albums'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added unique constraint None on '('slug',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed column 'albums.is_public'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'api_metrics.timestamp'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'blog_versions.display_date'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected NULL on column 'blog_versions.is_major_change'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'blog_versions.is_major_change'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blog_versions_is_major_change' on 'blog_versions'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.display_date'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.article_type'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.is_github_project'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.is_open_source'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.is_open_source'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.featured'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.display_order'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.homepage_order'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.homepage_order'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.published_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'ft_blogs_title_description' on 'blogs'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_article_type_featured' on 'blogs'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_article_type_open_source' on 'blogs'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_article_type' on 'blogs'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_homepage_display' on 'blogs'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_homepage_order' on 'blogs'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_type_published_homepage' on 'blogs'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_featured' on 'blogs'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_open_source' on 'blogs'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_blogs_date' on 'blogs'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_blogs_article_type' on '('article_type',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_blogs_display_date' on '('display_date',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed column 'blogs.has_detail_page'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed column 'blogs.tech_stack'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'careers.created_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'careers.updated_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.status'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.version'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.auto_save_interval'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.created_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.updated_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_content_type' on 'content_drafts'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_created_by' on 'content_drafts'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_status' on 'content_drafts'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_content_drafts_id' on '('id',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed foreign key (template_id)(id) on table content_drafts
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed foreign key (created_by)(id) on table content_drafts
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed foreign key (parent_draft_id)(id) on table content_drafts
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added foreign key (created_by)(id) on table content_drafts
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added foreign key (template_id)(id) on table content_drafts
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added foreign key (parent_draft_id)(id) on table content_drafts
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.template_type'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_active'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_default'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_system'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.usage_count'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.created_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.updated_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'content_templates'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_content_templates_id' on '('id',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed foreign key (created_by)(id) on table content_templates
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added foreign key (created_by)(id) on table content_templates
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'educations.created_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'educations.updated_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_category' on 'images'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_display_name' on 'images'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.enabled'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.display_order'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.page_type'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.created_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.updated_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'block_id' on 'layout_blocks'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_block_id' on 'layout_blocks'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_display_order' on 'layout_blocks'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'layout_blocks'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_layout_blocks_block_id' on '('block_id',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_layout_blocks_id' on '('id',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'navigation_items.created_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'navigation_items.updated_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.is_active'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.version'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.created_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.updated_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'page_layouts'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'page_layouts'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'page_type' on 'page_layouts'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_page_layouts_id' on '('id',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_page_layouts_page_type' on '('page_type',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_key'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_value'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.setting_type'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_type'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.page_type'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.content_id'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected NOT NULL on column 'seo_settings.is_active'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.is_active'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.is_active'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected NOT NULL on column 'seo_settings.priority'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.priority'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.priority'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.description'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'seo_settings.created_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.created_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'seo_settings.updated_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.updated_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_content_id' on 'seo_settings'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'seo_settings'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'seo_settings'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_key' on 'seo_settings'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_type' on 'seo_settings'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'setting_key' on 'seo_settings'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_content_id' on '('content_id',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_id' on '('id',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_is_active' on '('is_active',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_page_type' on '('page_type',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_setting_key' on '('setting_key',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_setting_type' on '('setting_type',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_key'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_value'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.setting_type'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_type'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.description'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'site_settings.created_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.created_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'site_settings.updated_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.updated_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_key' on 'site_settings'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_type' on 'site_settings'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'setting_key' on 'site_settings'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_id' on '('id',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_setting_key' on '('setting_key',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_setting_type' on '('setting_type',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'system_metrics_history.timestamp'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.memory_total'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.memory_used'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.memory_available'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.disk_total'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.disk_used'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.disk_free'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.color'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected column comment 'tags.icon'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.category'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.updated_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_category' on 'tags'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_icon' on 'tags'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_slug' on 'tags'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_category' on '('category',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_icon' on '('icon',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_slug' on '('slug',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'template_categories.is_universal'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.is_featured'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.is_active'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.usage_count'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.created_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.updated_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_category' on 'theme_presets'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_featured' on 'theme_presets'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_name' on 'theme_presets'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'theme_presets'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_theme_presets_id' on '('id',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_theme_presets_name' on '('name',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.theme_type'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_active'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_default'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_public'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.version'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.created_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.updated_at'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'themes'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_name' on 'themes'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_theme_type' on 'themes'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'themes'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_themes_id' on '('id',)'
2025-08-05 14:50:33 INFO  [alembic.autogenerate.compare] Detected added index 'ix_themes_name' on '('name',)'
2025-08-05 14:50:33 ERROR [alembic.util.messaging] New upgrade operations detected: [('remove_table', Table('project_categories_association', MetaData(), Column('project_id', INTEGER(), ForeignKey('projects.id'), table=<project_categories_association>, primary_key=True, nullable=False), Column('category_id', INTEGER(), ForeignKey('project_categories.id'), table=<project_categories_association>, primary_key=True, nullable=False), schema=None)), ('remove_index', Index('ix_project_module_categories_id', Column('id', INTEGER(), table=<project_module_categories>, primary_key=True, nullable=False))), ('remove_index', Index('ix_project_module_categories_slug', Column('slug', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<project_module_categories>, nullable=False, comment='URL友好标识符'), unique=True)), ('remove_index', Index('name', Column('name', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<project_module_categories>, nullable=False, comment='模块名称'), unique=True)), ('remove_table', Table('project_module_categories', MetaData(), Column('id', INTEGER(), table=<project_module_categories>, primary_key=True, nullable=False), Column('name', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<project_module_categories>, nullable=False, comment='模块名称'), Column('slug', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<project_module_categories>, nullable=False, comment='URL友好标识符'), Column('description', TEXT(charset='utf8mb4', collation='utf8mb4_unicode_ci'), table=<project_module_categories>, comment='模块描述'), Column('icon', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=50), table=<project_module_categories>, comment='模块图标 (Ant Design icon name or emoji:?)'), Column('display_order', INTEGER(), table=<project_module_categories>, nullable=False, comment='显示顺序，越小越靠前'), Column('is_active', TINYINT(display_width=1), table=<project_module_categories>, nullable=False, comment='是否在前台显示'), Column('created_at', DATETIME(), table=<project_module_categories>), Column('updated_at', DATETIME(), table=<project_module_categories>), schema=None)), ('remove_table', Table('project_module_categories_association', MetaData(), Column('project_id', INTEGER(), ForeignKey('projects.id'), table=<project_module_categories_association>, primary_key=True, nullable=False), Column('module_category_id', INTEGER(), ForeignKey('project_module_categories.id'), table=<project_module_categories_association>, primary_key=True, nullable=False), schema=None)), ('remove_table', Table('project_categories', MetaData(), Column('id', INTEGER(), table=<project_categories>, primary_key=True, nullable=False), Column('name', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<project_categories>, nullable=False), Column('slug', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<project_categories>, nullable=False), Column('created_at', DATETIME(), table=<project_categories>), Column('updated_at', DATETIME(), table=<project_categories>), Column('color', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=50), table=<project_categories>), schema=None)), ('remove_table', Table('categories', MetaData(), Column('id', INTEGER(), table=<categories>, primary_key=True, nullable=False), Column('name', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<categories>, nullable=False), Column('slug', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<categories>, nullable=False), Column('description', TEXT(charset='utf8mb4', collation='utf8mb4_unicode_ci'), table=<categories>), Column('created_at', DATETIME(), table=<categories>), Column('updated_at', DATETIME(), table=<categories>), Column('color', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=50), table=<categories>), schema=None)), ('remove_index', Index('ix_system_configs_id', Column('id', INTEGER(), table=<system_configs>, primary_key=True, nullable=False))), ('remove_index', Index('ix_system_configs_key', Column('key', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<system_configs>, nullable=False), unique=True)), ('remove_table', Table('system_configs', MetaData(), Column('id', INTEGER(), table=<system_configs>, primary_key=True, nullable=False), Column('key', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<system_configs>, nullable=False), Column('value', TEXT(collation='utf8mb4_unicode_ci'), table=<system_configs>), Column('description', VARCHAR(collation='utf8mb4_unicode_ci', length=255), table=<system_configs>), Column('config_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<system_configs>, nullable=False), Column('is_active', TINYINT(display_width=1), table=<system_configs>), Column('created_at', DATETIME(), table=<system_configs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800e2520>, for_update=False)), Column('updated_at', DATETIME(), table=<system_configs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800e2780>, for_update=False)), schema=None)), ('remove_index', Index('ix_icon_favorites_old_id', Column('id', INTEGER(), table=<icon_favorites_old>, primary_key=True, nullable=False))), ('remove_index', Index('uq_icon_session_favorite_old', Column('icon_id', INTEGER(), table=<icon_favorites_old>, nullable=False), Column('session_id', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<icon_favorites_old>, comment='会话ID'), unique=True)), ('remove_index', Index('uq_icon_user_favorite_old', Column('icon_id', INTEGER(), table=<icon_favorites_old>, nullable=False), Column('user_id', INTEGER(), table=<icon_favorites_old>, comment='用户ID'), unique=True)), ('remove_table', Table('icon_favorites_old', MetaData(), Column('id', INTEGER(), table=<icon_favorites_old>, primary_key=True, nullable=False), Column('icon_id', INTEGER(), ForeignKey('icon_metadata.id'), table=<icon_favorites_old>, nullable=False), Column('user_id', INTEGER(), table=<icon_favorites_old>, comment='用户ID'), Column('session_id', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<icon_favorites_old>, comment='会话ID'), Column('created_at', TIMESTAMP(), table=<icon_favorites_old>, nullable=False), schema=None)), ('remove_index', Index('ix_icon_favorites_id', Column('id', INTEGER(), table=<icon_favorites>, primary_key=True, nullable=False))), ('remove_index', Index('uq_icon_session_favorite', Column('icon_id', INTEGER(), table=<icon_favorites>, nullable=False), Column('session_id', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<icon_favorites>, comment='会话ID'), unique=True)), ('remove_index', Index('uq_icon_user_favorite', Column('icon_id', INTEGER(), table=<icon_favorites>, nullable=False), Column('user_id', INTEGER(), table=<icon_favorites>, comment='用户ID'), unique=True)), ('remove_table', Table('icon_favorites', MetaData(), Column('id', INTEGER(), table=<icon_favorites>, primary_key=True, nullable=False), Column('icon_id', INTEGER(), ForeignKey('icon_metadata.id'), table=<icon_favorites>, nullable=False), Column('user_id', INTEGER(), table=<icon_favorites>, comment='用户ID'), Column('session_id', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<icon_favorites>, comment='会话ID'), Column('created_at', TIMESTAMP(), table=<icon_favorites>, nullable=False), schema=None)), ('remove_index', Index('ix_projects_id', Column('id', INTEGER(), table=<projects>, primary_key=True, nullable=False))), ('remove_table', Table('projects', MetaData(), Column('id', INTEGER(), table=<projects>, primary_key=True, nullable=False), Column('name', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=255), table=<projects>, nullable=False), Column('description', TEXT(charset='utf8mb4', collation='utf8mb4_unicode_ci'), table=<projects>), Column('link', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=255), table=<projects>), Column('logo', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=255), table=<projects>), Column('featured', TINYINT(display_width=1), table=<projects>), Column('is_github_project', TINYINT(display_width=1), table=<projects>), Column('has_detail_page', TINYINT(display_width=1), table=<projects>), Column('detail_content', TEXT(charset='utf8mb4', collation='utf8mb4_unicode_ci'), table=<projects>), Column('project_order', INTEGER(), table=<projects>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800a1e80>, for_update=False)), Column('created_at', DATETIME(), table=<projects>), Column('updated_at', DATETIME(), table=<projects>), Column('icon', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<projects>), Column('show_on_homepage', TINYINT(display_width=1), table=<projects>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800d4550>, for_update=False), comment='是否在首页显示'), Column('homepage_order', INTEGER(), table=<projects>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800d4690>, for_update=False), comment='首页显示顺序'), schema=None)), [('modify_default', None, 'about_pages', 'photo_position', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e572fd0>, for_update=False), None)], [('modify_default', None, 'about_pages', 'photo_style', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e5ad780>, for_update=False), None)], [('modify_default', None, 'about_pages', 'photo_size', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e5ad8d0>, for_update=False), None)], [('modify_default', None, 'about_pages', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e593770>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x71d2802f4bd0; now>, for_update=False))], [('modify_default', None, 'about_pages', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e573850>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x71d2802f5150; now>, for_update=False))], [('modify_default', None, 'albums', 'type', {'existing_nullable': True, 'existing_type': ENUM('year', 'location', 'theme', 'category', collation='utf8mb4_unicode_ci'), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28151d970>, for_update=False), None)], [('modify_comment', None, 'albums', 'category', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=100), 'existing_server_default': False}, None, '相册分类')], [('modify_type', None, 'albums', 'slug', {'existing_nullable': True, 'existing_server_default': False, 'existing_comment': None}, VARCHAR(collation='utf8mb4_unicode_ci', length=255), String(length=100)), ('modify_comment', None, 'albums', 'slug', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=255), 'existing_server_default': False}, None, 'URL友好名称')], [('modify_default', None, 'albums', 'layout_type', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': '布局类型'}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007a4b0>, for_update=False), None), ('modify_comment', None, 'albums', 'layout_type', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007a4b0>, for_update=False)}, '布局类型', '布局类型: grid, masonry, carousel')], [('modify_default', None, 'albums', 'is_featured', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007a510>, for_update=False), None), ('modify_comment', None, 'albums', 'is_featured', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007a510>, for_update=False)}, None, '是否精选')], [('modify_default', None, 'albums', 'sort_order', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007a150>, for_update=False), None), ('modify_comment', None, 'albums', 'sort_order', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007a150>, for_update=False)}, None, '排序顺序')], ('remove_index', Index('idx_albums_category', Column('category', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<albums>))), ('remove_index', Index('idx_albums_is_featured', Column('is_featured', TINYINT(display_width=1), table=<albums>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007a510>, for_update=False)))), ('remove_index', Index('idx_albums_is_public', Column('is_public', TINYINT(display_width=1), table=<albums>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d280079eb0>, for_update=False)))), ('remove_index', Index('idx_albums_slug', Column('slug', VARCHAR(collation='utf8mb4_unicode_ci', length=255), table=<albums>))), ('remove_index', Index('idx_albums_sort_order', Column('sort_order', INTEGER(), table=<albums>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007a150>, for_update=False)))), ('add_constraint', UniqueConstraint(Column('slug', NullType(), table=<albums>))), ('remove_column', None, 'albums', Column('is_public', TINYINT(display_width=1), table=<albums>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d280079eb0>, for_update=False))), [('modify_default', None, 'api_metrics', 'timestamp', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007adb0>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x71d2801c3930; now>, for_update=False))], [('modify_comment', None, 'blog_versions', 'display_date', {'existing_nullable': False, 'existing_type': DATETIME(), 'existing_server_default': False}, '展示日期', None)], [('modify_nullable', None, 'blog_versions', 'is_major_change', {'existing_type': TINYINT(display_width=1), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007be30>, for_update=False), 'existing_comment': None}, False, True), ('modify_default', None, 'blog_versions', 'is_major_change', {'existing_nullable': False, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007be30>, for_update=False), None)], ('remove_index', Index('idx_blog_versions_is_major_change', Column('is_major_change', TINYINT(display_width=1), table=<blog_versions>, nullable=False, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007be30>, for_update=False)))), [('modify_comment', None, 'blogs', 'display_date', {'existing_nullable': False, 'existing_type': DATETIME(), 'existing_server_default': False}, '展示日期，用于前端显示和排序', None)], [('modify_default', None, 'blogs', 'article_type', {'existing_nullable': False, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800792b0>, for_update=False), None)], [('modify_default', None, 'blogs', 'is_github_project', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d280079130>, for_update=False), None)], [('modify_default', None, 'blogs', 'is_open_source', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': '是否开源项目'}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800788f0>, for_update=False), None), ('modify_comment', None, 'blogs', 'is_open_source', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800788f0>, for_update=False)}, '是否开源项目', None)], [('modify_default', None, 'blogs', 'featured', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800791f0>, for_update=False), None)], [('modify_default', None, 'blogs', 'display_order', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d280079310>, for_update=False), None)], [('modify_default', None, 'blogs', 'homepage_order', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': '首页显示顺序'}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d280079370>, for_update=False), None), ('modify_comment', None, 'blogs', 'homepage_order', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d280079370>, for_update=False)}, '首页显示顺序', None)], [('modify_comment', None, 'blogs', 'published_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_server_default': False}, '发布时间', None)], ('remove_index', Index('ft_blogs_title_description', Column('title', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=255), table=<blogs>, nullable=False), Column('description', TEXT(charset='utf8mb4', collation='utf8mb4_unicode_ci'), table=<blogs>))), ('remove_index', Index('idx_article_type_featured', Column('article_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<blogs>, nullable=False, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800792b0>, for_update=False)), Column('featured', TINYINT(display_width=1), table=<blogs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800791f0>, for_update=False)))), ('remove_index', Index('idx_article_type_open_source', Column('article_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<blogs>, nullable=False, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800792b0>, for_update=False)), Column('is_open_source', TINYINT(display_width=1), table=<blogs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800788f0>, for_update=False), comment='是否开源项目'))), ('remove_index', Index('idx_blogs_article_type', Column('article_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<blogs>, nullable=False, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800792b0>, for_update=False)))), ('remove_index', Index('idx_blogs_homepage_display', Column('show_on_homepage', TINYINT(display_width=1), table=<blogs>), Column('published', TINYINT(display_width=1), table=<blogs>), Column('article_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<blogs>, nullable=False, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800792b0>, for_update=False)), Column('homepage_order', INTEGER(), table=<blogs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d280079370>, for_update=False), comment='首页显示顺序'), Column('display_date', DATETIME(), table=<blogs>, nullable=False, comment='展示日期，用于前端显示和排序'))), ('remove_index', Index('idx_blogs_homepage_order', Column('homepage_order', INTEGER(), table=<blogs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d280079370>, for_update=False), comment='首页显示顺序'), Column('show_on_homepage', TINYINT(display_width=1), table=<blogs>))), ('remove_index', Index('idx_blogs_type_published_homepage', Column('article_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<blogs>, nullable=False, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800792b0>, for_update=False)), Column('published', TINYINT(display_width=1), table=<blogs>), Column('show_on_homepage', TINYINT(display_width=1), table=<blogs>))), ('remove_index', Index('idx_featured', Column('featured', TINYINT(display_width=1), table=<blogs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800791f0>, for_update=False)))), ('remove_index', Index('idx_is_open_source', Column('is_open_source', TINYINT(display_width=1), table=<blogs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800788f0>, for_update=False), comment='是否开源项目'))), ('remove_index', Index('ix_blogs_date', Column('display_date', DATETIME(), table=<blogs>, nullable=False, comment='展示日期，用于前端显示和排序'))), ('add_index', Index('ix_blogs_article_type', Column('article_type', String(length=50), table=<blogs>, nullable=False, default=ScalarElementColumnDefault('blog')))), ('add_index', Index('ix_blogs_display_date', Column('display_date', DateTime(), table=<blogs>, nullable=False))), ('remove_column', None, 'blogs', Column('has_detail_page', TINYINT(display_width=1), table=<blogs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d280079250>, for_update=False))), ('remove_column', None, 'blogs', Column('tech_stack', JSON(), table=<blogs>, comment='技术栈（JSON数组）')), [('modify_default', None, 'careers', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007bcb0>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x71d2801c2510; now>, for_update=False))], [('modify_default', None, 'careers', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e480530>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x71d2801c2580; now>, for_update=False))], [('modify_default', None, 'content_drafts', 'status', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=20), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4821b0>, for_update=False), None)], [('modify_default', None, 'content_drafts', 'version', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e482210>, for_update=False), None)], [('modify_default', None, 'content_drafts', 'auto_save_interval', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e482270>, for_update=False), None)], [('modify_default', None, 'content_drafts', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4822d0>, for_update=False), None)], [('modify_default', None, 'content_drafts', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e482330>, for_update=False), None)], ('remove_index', Index('idx_content_type', Column('content_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<content_drafts>, nullable=False))), ('remove_index', Index('idx_created_by', Column('created_by', INTEGER(), table=<content_drafts>, nullable=False))), ('remove_index', Index('idx_status', Column('status', VARCHAR(collation='utf8mb4_unicode_ci', length=20), table=<content_drafts>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4821b0>, for_update=False)))), ('add_index', Index('ix_content_drafts_id', Column('id', Integer(), table=<content_drafts>, primary_key=True, nullable=False))), ('remove_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x71d2800e8a40>, None, name='content_drafts_ibfk_1', ondelete='SET NULL', table=Table('content_drafts', MetaData(), Column('template_id', NullType(), ForeignKey('content_templates.id'), table=<content_drafts>), schema=None))), ('remove_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x71d27e2b89a0>, None, name='content_drafts_ibfk_3', ondelete='CASCADE', table=Table('content_drafts', MetaData(), Column('created_by', NullType(), ForeignKey('users.id'), table=<content_drafts>), schema=None))), ('remove_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x71d27e58ed40>, None, name='content_drafts_ibfk_2', ondelete='SET NULL', table=Table('content_drafts', MetaData(), Column('parent_draft_id', NullType(), ForeignKey('content_drafts.id'), table=<content_drafts>), Column('id', NullType(), table=<content_drafts>), schema=None))), ('add_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x71d27e19ddf0>, None, table=Table('content_drafts', MetaData(), Column('created_by', NullType(), ForeignKey('users.id'), table=<content_drafts>), schema=None))), ('add_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x71d27e19de40>, None, table=Table('content_drafts', MetaData(), Column('template_id', NullType(), ForeignKey('content_templates.id'), table=<content_drafts>), schema=None))), ('add_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x71d27e19de90>, None, table=Table('content_drafts', MetaData(), Column('parent_draft_id', NullType(), ForeignKey('content_drafts.id'), table=<content_drafts>), Column('id', NullType(), table=<content_drafts>), schema=None))), [('modify_default', None, 'content_templates', 'template_type', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e481250>, for_update=False), None)], [('modify_default', None, 'content_templates', 'is_active', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4811f0>, for_update=False), None)], [('modify_default', None, 'content_templates', 'is_default', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4812b0>, for_update=False), None)], [('modify_default', None, 'content_templates', 'is_system', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e481310>, for_update=False), None)], [('modify_default', None, 'content_templates', 'usage_count', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e481370>, for_update=False), None)], [('modify_default', None, 'content_templates', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4813d0>, for_update=False), None)], [('modify_default', None, 'content_templates', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e481430>, for_update=False), None)], ('remove_index', Index('idx_is_active', Column('is_active', TINYINT(display_width=1), table=<content_templates>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4811f0>, for_update=False)))), ('add_index', Index('ix_content_templates_id', Column('id', Integer(), table=<content_templates>, primary_key=True, nullable=False))), ('remove_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x71d27e19df30>, None, name='content_templates_ibfk_1', ondelete='SET NULL', table=Table('content_templates', MetaData(), Column('created_by', NullType(), ForeignKey('users.id'), table=<content_templates>), schema=None))), ('add_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x71d27e19dfd0>, None, table=Table('content_templates', MetaData(), Column('created_by', NullType(), ForeignKey('users.id'), table=<content_templates>), schema=None))), [('modify_default', None, 'educations', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007a9f0>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x71d2801c2040; now>, for_update=False))], [('modify_default', None, 'educations', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007ad50>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x71d2801c20b0; now>, for_update=False))], ('remove_index', Index('idx_category', Column('category_id', INTEGER(), table=<images>, comment='分类ID'))), ('remove_index', Index('idx_display_name', Column('display_name', VARCHAR(collation='utf8mb4_unicode_ci', length=255), table=<images>, comment='显示名称（用户可修改）'))), [('modify_default', None, 'layout_blocks', 'enabled', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d280153950>, for_update=False), None)], [('modify_default', None, 'layout_blocks', 'display_order', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800bad00>, for_update=False), None)], [('modify_default', None, 'layout_blocks', 'page_type', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800badb0>, for_update=False), None)], [('modify_default', None, 'layout_blocks', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e586f30>, for_update=False), None)], [('modify_default', None, 'layout_blocks', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e586350>, for_update=False), None)], ('remove_index', Index('block_id', Column('block_id', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<layout_blocks>, nullable=False), unique=True)), ('remove_index', Index('idx_block_id', Column('block_id', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<layout_blocks>, nullable=False))), ('remove_index', Index('idx_display_order', Column('display_order', INTEGER(), table=<layout_blocks>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800bad00>, for_update=False)))), ('remove_index', Index('idx_page_type', Column('page_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<layout_blocks>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800badb0>, for_update=False)))), ('add_index', Index('ix_layout_blocks_block_id', Column('block_id', String(length=100), table=<layout_blocks>, nullable=False), unique=True)), ('add_index', Index('ix_layout_blocks_id', Column('id', Integer(), table=<layout_blocks>, primary_key=True, nullable=False))), [('modify_default', None, 'navigation_items', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007b770>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x71d2801c2c80; now>, for_update=False))], [('modify_default', None, 'navigation_items', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007bad0>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x71d2801c2cf0; now>, for_update=False))], [('modify_default', None, 'page_layouts', 'is_active', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28009f410>, for_update=False), None)], [('modify_default', None, 'page_layouts', 'version', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=20), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28017b790>, for_update=False), None)], [('modify_default', None, 'page_layouts', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28017b460>, for_update=False), None)], [('modify_default', None, 'page_layouts', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800cd750>, for_update=False), None)], ('remove_index', Index('idx_is_active', Column('is_active', TINYINT(display_width=1), table=<page_layouts>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28009f410>, for_update=False)))), ('remove_index', Index('idx_page_type', Column('page_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<page_layouts>, nullable=False))), ('remove_index', Index('page_type', Column('page_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<page_layouts>, nullable=False), unique=True)), ('add_index', Index('ix_page_layouts_id', Column('id', Integer(), table=<page_layouts>, primary_key=True, nullable=False))), ('add_index', Index('ix_page_layouts_page_type', Column('page_type', String(length=50), table=<page_layouts>, nullable=False), unique=True)), [('modify_comment', None, 'seo_settings', 'setting_key', {'existing_nullable': False, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=100), 'existing_server_default': False}, 'SEO设置键名', None)], [('modify_comment', None, 'seo_settings', 'setting_value', {'existing_nullable': True, 'existing_type': JSON(), 'existing_server_default': False}, 'SEO设置值（JSON格式）', None)], [('modify_default', None, 'seo_settings', 'setting_type', {'existing_nullable': True, 'existing_type': ENUM('global', 'page', 'content', 'social', 'analytics', collation='utf8mb4_unicode_ci'), 'existing_comment': 'SEO设置类型'}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4826f0>, for_update=False), None), ('modify_comment', None, 'seo_settings', 'setting_type', {'existing_nullable': True, 'existing_type': ENUM('global', 'page', 'content', 'social', 'analytics', collation='utf8mb4_unicode_ci'), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4826f0>, for_update=False)}, 'SEO设置类型', None)], [('modify_comment', None, 'seo_settings', 'page_type', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_server_default': False}, '页面类型（homepage, blog, project, about等）', None)], [('modify_comment', None, 'seo_settings', 'content_id', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_server_default': False}, '内容ID（用于内容级别的SEO设置）', None)], [('modify_nullable', None, 'seo_settings', 'is_active', {'existing_type': TINYINT(display_width=1), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4827b0>, for_update=False), 'existing_comment': '是否启用'}, True, False), ('modify_default', None, 'seo_settings', 'is_active', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': '是否启用'}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4827b0>, for_update=False), None), ('modify_comment', None, 'seo_settings', 'is_active', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4827b0>, for_update=False)}, '是否启用', None)], [('modify_nullable', None, 'seo_settings', 'priority', {'existing_type': INTEGER(), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e482bd0>, for_update=False), 'existing_comment': '优先级（数字越大优先级越高）'}, True, False), ('modify_default', None, 'seo_settings', 'priority', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': '优先级（数字越大优先级越高）'}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e482bd0>, for_update=False), None), ('modify_comment', None, 'seo_settings', 'priority', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e482bd0>, for_update=False)}, '优先级（数字越大优先级越高）', None)], [('modify_comment', None, 'seo_settings', 'description', {'existing_nullable': True, 'existing_type': TEXT(collation='utf8mb4_unicode_ci'), 'existing_server_default': False}, 'SEO设置描述', None)], [('modify_type', None, 'seo_settings', 'created_at', {'existing_nullable': True, 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e482b70>, for_update=False), 'existing_comment': None}, TIMESTAMP(), DateTime()), ('modify_default', None, 'seo_settings', 'created_at', {'existing_nullable': True, 'existing_type': TIMESTAMP(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e482b70>, for_update=False), None)], [('modify_type', None, 'seo_settings', 'updated_at', {'existing_nullable': True, 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e482c30>, for_update=False), 'existing_comment': None}, TIMESTAMP(), DateTime()), ('modify_default', None, 'seo_settings', 'updated_at', {'existing_nullable': True, 'existing_type': TIMESTAMP(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e482c30>, for_update=False), None)], ('remove_index', Index('idx_content_id', Column('content_id', INTEGER(), table=<seo_settings>, comment='内容ID（用于内容级别的SEO设置）'))), ('remove_index', Index('idx_is_active', Column('is_active', TINYINT(display_width=1), table=<seo_settings>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4827b0>, for_update=False), comment='是否启用'))), ('remove_index', Index('idx_page_type', Column('page_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<seo_settings>, comment='页面类型（homepage, blog, project, about等）'))), ('remove_index', Index('idx_setting_key', Column('setting_key', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<seo_settings>, nullable=False, comment='SEO设置键名'))), ('remove_index', Index('idx_setting_type', Column('setting_type', ENUM('global', 'page', 'content', 'social', 'analytics'), table=<seo_settings>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4826f0>, for_update=False), comment='SEO设置类型'))), ('remove_index', Index('setting_key', Column('setting_key', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<seo_settings>, nullable=False, comment='SEO设置键名'), unique=True)), ('add_index', Index('ix_seo_settings_content_id', Column('content_id', Integer(), table=<seo_settings>))), ('add_index', Index('ix_seo_settings_id', Column('id', Integer(), table=<seo_settings>, primary_key=True, nullable=False))), ('add_index', Index('ix_seo_settings_is_active', Column('is_active', Boolean(), table=<seo_settings>, nullable=False, default=ScalarElementColumnDefault(True)))), ('add_index', Index('ix_seo_settings_page_type', Column('page_type', String(length=50), table=<seo_settings>))), ('add_index', Index('ix_seo_settings_setting_key', Column('setting_key', String(length=100), table=<seo_settings>, nullable=False), unique=True)), ('add_index', Index('ix_seo_settings_setting_type', Column('setting_type', Enum('global', 'page', 'content', 'social', 'analytics'), table=<seo_settings>, default=ScalarElementColumnDefault('global')))), ('remove_table_comment', Table('seo_settings', MetaData(), schema=None)), [('modify_comment', None, 'site_settings', 'setting_key', {'existing_nullable': False, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=100), 'existing_server_default': False}, '设置键名', None)], [('modify_comment', None, 'site_settings', 'setting_value', {'existing_nullable': True, 'existing_type': JSON(), 'existing_server_default': False}, '设置值（JSON格式）', None)], [('modify_default', None, 'site_settings', 'setting_type', {'existing_nullable': True, 'existing_type': ENUM('personal_info', 'theme', 'social_links', 'navigation', 'general', collation='utf8mb4_unicode_ci'), 'existing_comment': '设置类型'}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e481490>, for_update=False), None), ('modify_comment', None, 'site_settings', 'setting_type', {'existing_nullable': True, 'existing_type': ENUM('personal_info', 'theme', 'social_links', 'navigation', 'general', collation='utf8mb4_unicode_ci'), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e481490>, for_update=False)}, '设置类型', None)], [('modify_comment', None, 'site_settings', 'description', {'existing_nullable': True, 'existing_type': TEXT(collation='utf8mb4_unicode_ci'), 'existing_server_default': False}, '设置描述', None)], [('modify_type', None, 'site_settings', 'created_at', {'existing_nullable': True, 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e480d70>, for_update=False), 'existing_comment': None}, TIMESTAMP(), DateTime()), ('modify_default', None, 'site_settings', 'created_at', {'existing_nullable': True, 'existing_type': TIMESTAMP(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e480d70>, for_update=False), None)], [('modify_type', None, 'site_settings', 'updated_at', {'existing_nullable': True, 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4815b0>, for_update=False), 'existing_comment': None}, TIMESTAMP(), DateTime()), ('modify_default', None, 'site_settings', 'updated_at', {'existing_nullable': True, 'existing_type': TIMESTAMP(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4815b0>, for_update=False), None)], ('remove_index', Index('idx_setting_key', Column('setting_key', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<site_settings>, nullable=False, comment='设置键名'))), ('remove_index', Index('idx_setting_type', Column('setting_type', ENUM('personal_info', 'theme', 'social_links', 'navigation', 'general'), table=<site_settings>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e481490>, for_update=False), comment='设置类型'))), ('remove_index', Index('setting_key', Column('setting_key', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<site_settings>, nullable=False, comment='设置键名'), unique=True)), ('add_index', Index('ix_site_settings_id', Column('id', Integer(), table=<site_settings>, primary_key=True, nullable=False))), ('add_index', Index('ix_site_settings_setting_key', Column('setting_key', String(length=100), table=<site_settings>, nullable=False), unique=True)), ('add_index', Index('ix_site_settings_setting_type', Column('setting_type', Enum('personal_info', 'theme', 'social_links', 'navigation', 'general'), table=<site_settings>, default=ScalarElementColumnDefault('general')))), ('remove_table_comment', Table('site_settings', MetaData(), schema=None)), [('modify_default', None, 'system_metrics_history', 'timestamp', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d28007a5d0>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x71d2801c3700; now>, for_update=False))], [('modify_type', None, 'system_metrics_history', 'memory_total', {'existing_nullable': True, 'existing_server_default': False, 'existing_comment': None}, BIGINT(), Integer())], [('modify_type', None, 'system_metrics_history', 'memory_used', {'existing_nullable': True, 'existing_server_default': False, 'existing_comment': None}, BIGINT(), Integer())], [('modify_type', None, 'system_metrics_history', 'memory_available', {'existing_nullable': True, 'existing_server_default': False, 'existing_comment': None}, BIGINT(), Integer())], [('modify_type', None, 'system_metrics_history', 'disk_total', {'existing_nullable': True, 'existing_server_default': False, 'existing_comment': None}, BIGINT(), Integer())], [('modify_type', None, 'system_metrics_history', 'disk_used', {'existing_nullable': True, 'existing_server_default': False, 'existing_comment': None}, BIGINT(), Integer())], [('modify_type', None, 'system_metrics_history', 'disk_free', {'existing_nullable': True, 'existing_server_default': False, 'existing_comment': None}, BIGINT(), Integer())], [('modify_default', None, 'tags', 'color', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d280079910>, for_update=False), None)], [('modify_comment', None, 'tags', 'icon', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=100), 'existing_server_default': False}, '标签图标，支持统一图标管理系统格式 (library:icon_key)', None)], [('modify_default', None, 'tags', 'category', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d280079850>, for_update=False), None)], [('modify_default', None, 'tags', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d280079970>, for_update=False), None)], ('remove_index', Index('idx_tags_category', Column('category', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<tags>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d280079850>, for_update=False)))), ('remove_index', Index('idx_tags_icon', Column('icon', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<tags>, comment='标签图标，支持统一图标管理系统格式 (library:icon_key)'))), ('remove_index', Index('idx_tags_slug', Column('slug', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<tags>), unique=True)), ('add_index', Index('ix_tags_category', Column('category', String(length=50), table=<tags>, default=ScalarElementColumnDefault('content')))), ('add_index', Index('ix_tags_icon', Column('icon', String(length=100), table=<tags>))), ('add_index', Index('ix_tags_slug', Column('slug', String(length=100), table=<tags>), unique=True)), [('modify_default', None, 'template_categories', 'is_universal', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4809b0>, for_update=False), None)], [('modify_default', None, 'theme_presets', 'is_featured', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e483050>, for_update=False), None)], [('modify_default', None, 'theme_presets', 'is_active', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e483410>, for_update=False), None)], [('modify_default', None, 'theme_presets', 'usage_count', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4833b0>, for_update=False), None)], [('modify_default', None, 'theme_presets', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e483470>, for_update=False), None)], [('modify_default', None, 'theme_presets', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e4834d0>, for_update=False), None)], ('remove_index', Index('idx_category', Column('category', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<theme_presets>))), ('remove_index', Index('idx_is_featured', Column('is_featured', TINYINT(display_width=1), table=<theme_presets>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e483050>, for_update=False)))), ('remove_index', Index('idx_name', Column('name', VARCHAR(collation='utf8mb4_unicode_ci', length=255), table=<theme_presets>, nullable=False))), ('remove_index', Index('name', Column('name', VARCHAR(collation='utf8mb4_unicode_ci', length=255), table=<theme_presets>, nullable=False), unique=True)), ('add_index', Index('ix_theme_presets_id', Column('id', Integer(), table=<theme_presets>, primary_key=True, nullable=False))), ('add_index', Index('ix_theme_presets_name', Column('name', String(length=255), table=<theme_presets>, nullable=False), unique=True)), [('modify_default', None, 'themes', 'theme_type', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800cd550>, for_update=False), None)], [('modify_default', None, 'themes', 'is_active', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e54e210>, for_update=False), None)], [('modify_default', None, 'themes', 'is_default', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e54e300>, for_update=False), None)], [('modify_default', None, 'themes', 'is_public', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e5a91d0>, for_update=False), None)], [('modify_default', None, 'themes', 'version', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=20), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e5a9470>, for_update=False), None)], [('modify_default', None, 'themes', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800ffd40>, for_update=False), None)], [('modify_default', None, 'themes', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2801537d0>, for_update=False), None)], ('remove_index', Index('idx_is_active', Column('is_active', TINYINT(display_width=1), table=<themes>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d27e54e210>, for_update=False)))), ('remove_index', Index('idx_name', Column('name', VARCHAR(collation='utf8mb4_unicode_ci', length=255), table=<themes>, nullable=False))), ('remove_index', Index('idx_theme_type', Column('theme_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<themes>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x71d2800cd550>, for_update=False)))), ('remove_index', Index('name', Column('name', VARCHAR(collation='utf8mb4_unicode_ci', length=255), table=<themes>, nullable=False), unique=True)), ('add_index', Index('ix_themes_id', Column('id', Integer(), table=<themes>, primary_key=True, nullable=False))), ('add_index', Index('ix_themes_name', Column('name', String(length=255), table=<themes>, nullable=False), unique=True))]
2025-08-05 14:52:01 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 14:52:01 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 14:52:01 ERROR [alembic.util.messaging] Can't locate revision identified by 'c9c1c6a1dc4e'
2025-08-05 14:52:22 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 14:52:22 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 14:52:22 INFO  [alembic.runtime.migration] Running stamp_revision  -> d5ebd9584e9d
2025-08-05 14:52:31 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 14:52:31 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 14:52:50 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 14:52:50 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_icon_favorites_old_id' on 'icon_favorites_old'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_session_favorite_old' on 'icon_favorites_old'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_user_favorite_old' on 'icon_favorites_old'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed table 'icon_favorites_old'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_module_categories_id' on 'project_module_categories'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_module_categories_slug' on 'project_module_categories'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'project_module_categories'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed table 'project_module_categories'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed table 'project_categories'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed table 'project_module_categories_association'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_system_configs_id' on 'system_configs'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_system_configs_key' on 'system_configs'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed table 'system_configs'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_projects_id' on 'projects'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed table 'projects'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_icon_favorites_id' on 'icon_favorites'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_session_favorite' on 'icon_favorites'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_user_favorite' on 'icon_favorites'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed table 'icon_favorites'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed table 'categories'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed table 'project_categories_association'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_position'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_style'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_size'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.created_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.updated_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.type'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.category'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected type change from VARCHAR(collation='utf8mb4_unicode_ci', length=255) to String(length=100) on 'albums.slug'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.slug'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.layout_type'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.layout_type'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.is_featured'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.is_featured'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.sort_order'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.sort_order'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_category' on 'albums'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_is_featured' on 'albums'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_is_public' on 'albums'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_slug' on 'albums'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_sort_order' on 'albums'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added unique constraint None on '('slug',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed column 'albums.is_public'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'api_metrics.timestamp'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'blog_versions.display_date'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected NULL on column 'blog_versions.is_major_change'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'blog_versions.is_major_change'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blog_versions_is_major_change' on 'blog_versions'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.display_date'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.article_type'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.is_github_project'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.is_open_source'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.is_open_source'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.featured'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.display_order'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.homepage_order'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.homepage_order'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.published_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'ft_blogs_title_description' on 'blogs'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_article_type_featured' on 'blogs'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_article_type_open_source' on 'blogs'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_article_type' on 'blogs'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_homepage_display' on 'blogs'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_homepage_order' on 'blogs'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_type_published_homepage' on 'blogs'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_featured' on 'blogs'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_open_source' on 'blogs'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_blogs_date' on 'blogs'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_blogs_article_type' on '('article_type',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_blogs_display_date' on '('display_date',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed column 'blogs.has_detail_page'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed column 'blogs.tech_stack'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'careers.created_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'careers.updated_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.status'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.version'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.auto_save_interval'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.created_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.updated_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_content_type' on 'content_drafts'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_created_by' on 'content_drafts'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_status' on 'content_drafts'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_content_drafts_id' on '('id',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed foreign key (template_id)(id) on table content_drafts
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed foreign key (created_by)(id) on table content_drafts
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed foreign key (parent_draft_id)(id) on table content_drafts
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added foreign key (created_by)(id) on table content_drafts
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added foreign key (template_id)(id) on table content_drafts
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added foreign key (parent_draft_id)(id) on table content_drafts
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.template_type'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_active'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_default'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_system'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.usage_count'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.created_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.updated_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'content_templates'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_content_templates_id' on '('id',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed foreign key (created_by)(id) on table content_templates
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added foreign key (created_by)(id) on table content_templates
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'educations.created_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'educations.updated_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_category' on 'images'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_display_name' on 'images'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.enabled'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.display_order'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.page_type'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.created_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.updated_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'block_id' on 'layout_blocks'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_block_id' on 'layout_blocks'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_display_order' on 'layout_blocks'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'layout_blocks'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_layout_blocks_block_id' on '('block_id',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_layout_blocks_id' on '('id',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'navigation_items.created_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'navigation_items.updated_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.is_active'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.version'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.created_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.updated_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'page_layouts'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'page_layouts'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'page_type' on 'page_layouts'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_page_layouts_id' on '('id',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_page_layouts_page_type' on '('page_type',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_key'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_value'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.setting_type'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_type'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.page_type'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.content_id'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected NOT NULL on column 'seo_settings.is_active'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.is_active'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.is_active'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected NOT NULL on column 'seo_settings.priority'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.priority'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.priority'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.description'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'seo_settings.created_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.created_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'seo_settings.updated_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.updated_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_content_id' on 'seo_settings'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'seo_settings'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'seo_settings'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_key' on 'seo_settings'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_type' on 'seo_settings'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'setting_key' on 'seo_settings'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_content_id' on '('content_id',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_id' on '('id',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_is_active' on '('is_active',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_page_type' on '('page_type',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_setting_key' on '('setting_key',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_setting_type' on '('setting_type',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_key'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_value'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.setting_type'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_type'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.description'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'site_settings.created_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.created_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'site_settings.updated_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.updated_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_key' on 'site_settings'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_type' on 'site_settings'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'setting_key' on 'site_settings'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_id' on '('id',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_setting_key' on '('setting_key',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_setting_type' on '('setting_type',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'system_metrics_history.timestamp'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.memory_total'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.memory_used'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.memory_available'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.disk_total'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.disk_used'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.disk_free'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.color'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected column comment 'tags.icon'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.category'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.updated_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_category' on 'tags'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_icon' on 'tags'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_slug' on 'tags'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_category' on '('category',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_icon' on '('icon',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_slug' on '('slug',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'template_categories.is_universal'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.is_featured'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.is_active'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.usage_count'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.created_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.updated_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_category' on 'theme_presets'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_featured' on 'theme_presets'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_name' on 'theme_presets'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'theme_presets'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_theme_presets_id' on '('id',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_theme_presets_name' on '('name',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.theme_type'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_active'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_default'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_public'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.version'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.created_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.updated_at'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'themes'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_name' on 'themes'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_theme_type' on 'themes'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'themes'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_themes_id' on '('id',)'
2025-08-05 14:52:50 INFO  [alembic.autogenerate.compare] Detected added index 'ix_themes_name' on '('name',)'
2025-08-05 14:53:04 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 14:53:04 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 14:53:12 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 14:53:12 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_projects_id' on 'projects'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed table 'projects'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_icon_favorites_old_id' on 'icon_favorites_old'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_session_favorite_old' on 'icon_favorites_old'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_user_favorite_old' on 'icon_favorites_old'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed table 'icon_favorites_old'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed table 'categories'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_module_categories_id' on 'project_module_categories'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_module_categories_slug' on 'project_module_categories'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'project_module_categories'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed table 'project_module_categories'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed table 'project_module_categories_association'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed table 'project_categories'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed table 'project_categories_association'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_icon_favorites_id' on 'icon_favorites'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_session_favorite' on 'icon_favorites'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_user_favorite' on 'icon_favorites'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed table 'icon_favorites'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_system_configs_id' on 'system_configs'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_system_configs_key' on 'system_configs'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed table 'system_configs'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_position'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_style'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_size'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.created_at'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.updated_at'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.type'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.category'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected type change from VARCHAR(collation='utf8mb4_unicode_ci', length=255) to String(length=100) on 'albums.slug'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.slug'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.layout_type'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.layout_type'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.is_featured'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.is_featured'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.sort_order'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.sort_order'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_category' on 'albums'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_is_featured' on 'albums'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_is_public' on 'albums'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_slug' on 'albums'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_sort_order' on 'albums'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected added unique constraint None on '('slug',)'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed column 'albums.is_public'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'api_metrics.timestamp'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected column comment 'blog_versions.display_date'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected NULL on column 'blog_versions.is_major_change'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'blog_versions.is_major_change'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blog_versions_is_major_change' on 'blog_versions'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.display_date'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.article_type'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.is_github_project'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.is_open_source'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.is_open_source'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.featured'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.display_order'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.homepage_order'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.homepage_order'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.published_at'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'ft_blogs_title_description' on 'blogs'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_article_type_featured' on 'blogs'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_article_type_open_source' on 'blogs'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_article_type' on 'blogs'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_homepage_display' on 'blogs'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_homepage_order' on 'blogs'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_type_published_homepage' on 'blogs'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_featured' on 'blogs'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_open_source' on 'blogs'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_blogs_date' on 'blogs'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected added index 'ix_blogs_article_type' on '('article_type',)'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected added index 'ix_blogs_display_date' on '('display_date',)'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed column 'blogs.has_detail_page'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed column 'blogs.tech_stack'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'careers.created_at'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'careers.updated_at'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.status'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.version'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.auto_save_interval'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.created_at'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.updated_at'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_content_type' on 'content_drafts'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_created_by' on 'content_drafts'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_status' on 'content_drafts'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected added index 'ix_content_drafts_id' on '('id',)'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed foreign key (created_by)(id) on table content_drafts
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed foreign key (parent_draft_id)(id) on table content_drafts
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed foreign key (template_id)(id) on table content_drafts
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected added foreign key (created_by)(id) on table content_drafts
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected added foreign key (parent_draft_id)(id) on table content_drafts
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected added foreign key (template_id)(id) on table content_drafts
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.template_type'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_active'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_default'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_system'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.usage_count'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.created_at'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.updated_at'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'content_templates'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected added index 'ix_content_templates_id' on '('id',)'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected removed foreign key (created_by)(id) on table content_templates
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected added foreign key (created_by)(id) on table content_templates
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'educations.created_at'
2025-08-05 14:53:12 INFO  [alembic.autogenerate.compare] Detected server default on column 'educations.updated_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_category' on 'images'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_display_name' on 'images'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.enabled'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.display_order'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.page_type'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.created_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.updated_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'block_id' on 'layout_blocks'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_block_id' on 'layout_blocks'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_display_order' on 'layout_blocks'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'layout_blocks'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_layout_blocks_block_id' on '('block_id',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_layout_blocks_id' on '('id',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'navigation_items.created_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'navigation_items.updated_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.is_active'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.version'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.created_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.updated_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'page_layouts'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'page_layouts'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'page_type' on 'page_layouts'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_page_layouts_id' on '('id',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_page_layouts_page_type' on '('page_type',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_key'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_value'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.setting_type'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_type'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.page_type'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.content_id'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected NOT NULL on column 'seo_settings.is_active'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.is_active'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.is_active'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected NOT NULL on column 'seo_settings.priority'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.priority'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.priority'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.description'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'seo_settings.created_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.created_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'seo_settings.updated_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.updated_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_content_id' on 'seo_settings'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'seo_settings'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'seo_settings'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_key' on 'seo_settings'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_type' on 'seo_settings'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'setting_key' on 'seo_settings'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_content_id' on '('content_id',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_id' on '('id',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_is_active' on '('is_active',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_page_type' on '('page_type',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_setting_key' on '('setting_key',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_setting_type' on '('setting_type',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_key'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_value'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.setting_type'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_type'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.description'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'site_settings.created_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.created_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'site_settings.updated_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.updated_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_key' on 'site_settings'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_type' on 'site_settings'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'setting_key' on 'site_settings'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_id' on '('id',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_setting_key' on '('setting_key',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_setting_type' on '('setting_type',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'system_metrics_history.timestamp'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.memory_total'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.memory_used'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.memory_available'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.disk_total'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.disk_used'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.disk_free'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.color'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected column comment 'tags.icon'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.category'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.updated_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_category' on 'tags'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_icon' on 'tags'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_slug' on 'tags'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_category' on '('category',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_icon' on '('icon',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_slug' on '('slug',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'template_categories.is_universal'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.is_featured'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.is_active'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.usage_count'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.created_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.updated_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_category' on 'theme_presets'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_featured' on 'theme_presets'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_name' on 'theme_presets'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'theme_presets'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_theme_presets_id' on '('id',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_theme_presets_name' on '('name',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.theme_type'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_active'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_default'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_public'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.version'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.created_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.updated_at'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'themes'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_name' on 'themes'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_theme_type' on 'themes'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'themes'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_themes_id' on '('id',)'
2025-08-05 14:53:13 INFO  [alembic.autogenerate.compare] Detected added index 'ix_themes_name' on '('name',)'
2025-08-05 14:53:13 ERROR [alembic.util.messaging] New upgrade operations detected: [('remove_index', Index('ix_projects_id', Column('id', INTEGER(), table=<projects>, primary_key=True, nullable=False))), ('remove_table', Table('projects', MetaData(), Column('id', INTEGER(), table=<projects>, primary_key=True, nullable=False), Column('name', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=255), table=<projects>, nullable=False), Column('description', TEXT(charset='utf8mb4', collation='utf8mb4_unicode_ci'), table=<projects>), Column('link', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=255), table=<projects>), Column('logo', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=255), table=<projects>), Column('featured', TINYINT(display_width=1), table=<projects>), Column('is_github_project', TINYINT(display_width=1), table=<projects>), Column('has_detail_page', TINYINT(display_width=1), table=<projects>), Column('detail_content', TEXT(charset='utf8mb4', collation='utf8mb4_unicode_ci'), table=<projects>), Column('project_order', INTEGER(), table=<projects>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9daa1940>, for_update=False)), Column('created_at', DATETIME(), table=<projects>), Column('updated_at', DATETIME(), table=<projects>), Column('icon', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<projects>), Column('show_on_homepage', TINYINT(display_width=1), table=<projects>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9da07610>, for_update=False), comment='是否在首页显示'), Column('homepage_order', INTEGER(), table=<projects>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9da07750>, for_update=False), comment='首页显示顺序'), schema=None)), ('remove_index', Index('ix_icon_favorites_old_id', Column('id', INTEGER(), table=<icon_favorites_old>, primary_key=True, nullable=False))), ('remove_index', Index('uq_icon_session_favorite_old', Column('icon_id', INTEGER(), table=<icon_favorites_old>, nullable=False), Column('session_id', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<icon_favorites_old>, comment='会话ID'), unique=True)), ('remove_index', Index('uq_icon_user_favorite_old', Column('icon_id', INTEGER(), table=<icon_favorites_old>, nullable=False), Column('user_id', INTEGER(), table=<icon_favorites_old>, comment='用户ID'), unique=True)), ('remove_table', Table('icon_favorites_old', MetaData(), Column('id', INTEGER(), table=<icon_favorites_old>, primary_key=True, nullable=False), Column('icon_id', INTEGER(), ForeignKey('icon_metadata.id'), table=<icon_favorites_old>, nullable=False), Column('user_id', INTEGER(), table=<icon_favorites_old>, comment='用户ID'), Column('session_id', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<icon_favorites_old>, comment='会话ID'), Column('created_at', TIMESTAMP(), table=<icon_favorites_old>, nullable=False), schema=None)), ('remove_table', Table('categories', MetaData(), Column('id', INTEGER(), table=<categories>, primary_key=True, nullable=False), Column('name', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<categories>, nullable=False), Column('slug', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<categories>, nullable=False), Column('description', TEXT(charset='utf8mb4', collation='utf8mb4_unicode_ci'), table=<categories>), Column('created_at', DATETIME(), table=<categories>), Column('updated_at', DATETIME(), table=<categories>), Column('color', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=50), table=<categories>), schema=None)), ('remove_index', Index('ix_project_module_categories_id', Column('id', INTEGER(), table=<project_module_categories>, primary_key=True, nullable=False))), ('remove_index', Index('ix_project_module_categories_slug', Column('slug', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<project_module_categories>, nullable=False, comment='URL友好标识符'), unique=True)), ('remove_index', Index('name', Column('name', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<project_module_categories>, nullable=False, comment='模块名称'), unique=True)), ('remove_table', Table('project_module_categories', MetaData(), Column('id', INTEGER(), table=<project_module_categories>, primary_key=True, nullable=False), Column('name', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<project_module_categories>, nullable=False, comment='模块名称'), Column('slug', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<project_module_categories>, nullable=False, comment='URL友好标识符'), Column('description', TEXT(charset='utf8mb4', collation='utf8mb4_unicode_ci'), table=<project_module_categories>, comment='模块描述'), Column('icon', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=50), table=<project_module_categories>, comment='模块图标 (Ant Design icon name or emoji:?)'), Column('display_order', INTEGER(), table=<project_module_categories>, nullable=False, comment='显示顺序，越小越靠前'), Column('is_active', TINYINT(display_width=1), table=<project_module_categories>, nullable=False, comment='是否在前台显示'), Column('created_at', DATETIME(), table=<project_module_categories>), Column('updated_at', DATETIME(), table=<project_module_categories>), schema=None)), ('remove_table', Table('project_module_categories_association', MetaData(), Column('project_id', INTEGER(), ForeignKey('projects.id'), table=<project_module_categories_association>, primary_key=True, nullable=False), Column('module_category_id', INTEGER(), ForeignKey('project_module_categories.id'), table=<project_module_categories_association>, primary_key=True, nullable=False), schema=None)), ('remove_table', Table('project_categories', MetaData(), Column('id', INTEGER(), table=<project_categories>, primary_key=True, nullable=False), Column('name', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<project_categories>, nullable=False), Column('slug', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=100), table=<project_categories>, nullable=False), Column('created_at', DATETIME(), table=<project_categories>), Column('updated_at', DATETIME(), table=<project_categories>), Column('color', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=50), table=<project_categories>), schema=None)), ('remove_table', Table('project_categories_association', MetaData(), Column('project_id', INTEGER(), ForeignKey('projects.id'), table=<project_categories_association>, primary_key=True, nullable=False), Column('category_id', INTEGER(), ForeignKey('project_categories.id'), table=<project_categories_association>, primary_key=True, nullable=False), schema=None)), ('remove_index', Index('ix_icon_favorites_id', Column('id', INTEGER(), table=<icon_favorites>, primary_key=True, nullable=False))), ('remove_index', Index('uq_icon_session_favorite', Column('icon_id', INTEGER(), table=<icon_favorites>, nullable=False), Column('session_id', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<icon_favorites>, comment='会话ID'), unique=True)), ('remove_index', Index('uq_icon_user_favorite', Column('icon_id', INTEGER(), table=<icon_favorites>, nullable=False), Column('user_id', INTEGER(), table=<icon_favorites>, comment='用户ID'), unique=True)), ('remove_table', Table('icon_favorites', MetaData(), Column('id', INTEGER(), table=<icon_favorites>, primary_key=True, nullable=False), Column('icon_id', INTEGER(), ForeignKey('icon_metadata.id'), table=<icon_favorites>, nullable=False), Column('user_id', INTEGER(), table=<icon_favorites>, comment='用户ID'), Column('session_id', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<icon_favorites>, comment='会话ID'), Column('created_at', TIMESTAMP(), table=<icon_favorites>, nullable=False), schema=None)), ('remove_index', Index('ix_system_configs_id', Column('id', INTEGER(), table=<system_configs>, primary_key=True, nullable=False))), ('remove_index', Index('ix_system_configs_key', Column('key', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<system_configs>, nullable=False), unique=True)), ('remove_table', Table('system_configs', MetaData(), Column('id', INTEGER(), table=<system_configs>, primary_key=True, nullable=False), Column('key', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<system_configs>, nullable=False), Column('value', TEXT(collation='utf8mb4_unicode_ci'), table=<system_configs>), Column('description', VARCHAR(collation='utf8mb4_unicode_ci', length=255), table=<system_configs>), Column('config_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<system_configs>, nullable=False), Column('is_active', TINYINT(display_width=1), table=<system_configs>), Column('created_at', DATETIME(), table=<system_configs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf669e0>, for_update=False)), Column('updated_at', DATETIME(), table=<system_configs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf66c40>, for_update=False)), schema=None)), [('modify_default', None, 'about_pages', 'photo_position', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6bd10>, for_update=False), None)], [('modify_default', None, 'about_pages', 'photo_style', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6bcb0>, for_update=False), None)], [('modify_default', None, 'about_pages', 'photo_size', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6bd70>, for_update=False), None)], [('modify_default', None, 'about_pages', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6ba70>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x7c6b9dcf8bd0; now>, for_update=False))], [('modify_default', None, 'about_pages', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6bad0>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x7c6b9dcf9150; now>, for_update=False))], [('modify_default', None, 'albums', 'type', {'existing_nullable': True, 'existing_type': ENUM('year', 'location', 'theme', 'category', collation='utf8mb4_unicode_ci'), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf69910>, for_update=False), None)], [('modify_comment', None, 'albums', 'category', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=100), 'existing_server_default': False}, None, '相册分类')], [('modify_type', None, 'albums', 'slug', {'existing_nullable': True, 'existing_server_default': False, 'existing_comment': None}, VARCHAR(collation='utf8mb4_unicode_ci', length=255), String(length=100)), ('modify_comment', None, 'albums', 'slug', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=255), 'existing_server_default': False}, None, 'URL友好名称')], [('modify_default', None, 'albums', 'layout_type', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': '布局类型'}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6a150>, for_update=False), None), ('modify_comment', None, 'albums', 'layout_type', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6a150>, for_update=False)}, '布局类型', '布局类型: grid, masonry, carousel')], [('modify_default', None, 'albums', 'is_featured', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6a1b0>, for_update=False), None), ('modify_comment', None, 'albums', 'is_featured', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6a1b0>, for_update=False)}, None, '是否精选')], [('modify_default', None, 'albums', 'sort_order', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf69a30>, for_update=False), None), ('modify_comment', None, 'albums', 'sort_order', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf69a30>, for_update=False)}, None, '排序顺序')], ('remove_index', Index('idx_albums_category', Column('category', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<albums>))), ('remove_index', Index('idx_albums_is_featured', Column('is_featured', TINYINT(display_width=1), table=<albums>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6a1b0>, for_update=False)))), ('remove_index', Index('idx_albums_is_public', Column('is_public', TINYINT(display_width=1), table=<albums>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf69a90>, for_update=False)))), ('remove_index', Index('idx_albums_slug', Column('slug', VARCHAR(collation='utf8mb4_unicode_ci', length=255), table=<albums>))), ('remove_index', Index('idx_albums_sort_order', Column('sort_order', INTEGER(), table=<albums>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf69a30>, for_update=False)))), ('add_constraint', UniqueConstraint(Column('slug', NullType(), table=<albums>))), ('remove_column', None, 'albums', Column('is_public', TINYINT(display_width=1), table=<albums>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf69a90>, for_update=False))), [('modify_default', None, 'api_metrics', 'timestamp', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9ef1fad0>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x7c6b9dbc3930; now>, for_update=False))], [('modify_comment', None, 'blog_versions', 'display_date', {'existing_nullable': False, 'existing_type': DATETIME(), 'existing_server_default': False}, '展示日期', None)], [('modify_nullable', None, 'blog_versions', 'is_major_change', {'existing_type': TINYINT(display_width=1), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf697f0>, for_update=False), 'existing_comment': None}, False, True), ('modify_default', None, 'blog_versions', 'is_major_change', {'existing_nullable': False, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf697f0>, for_update=False), None)], ('remove_index', Index('idx_blog_versions_is_major_change', Column('is_major_change', TINYINT(display_width=1), table=<blog_versions>, nullable=False, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf697f0>, for_update=False)))), [('modify_comment', None, 'blogs', 'display_date', {'existing_nullable': False, 'existing_type': DATETIME(), 'existing_server_default': False}, '展示日期，用于前端显示和排序', None)], [('modify_default', None, 'blogs', 'article_type', {'existing_nullable': False, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9dab6c50>, for_update=False), None)], [('modify_default', None, 'blogs', 'is_github_project', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9dab6d00>, for_update=False), None)], [('modify_default', None, 'blogs', 'is_open_source', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': '是否开源项目'}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf77e30>, for_update=False), None), ('modify_comment', None, 'blogs', 'is_open_source', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf77e30>, for_update=False)}, '是否开源项目', None)], [('modify_default', None, 'blogs', 'featured', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf76490>, for_update=False), None)], [('modify_default', None, 'blogs', 'display_order', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf858d0>, for_update=False), None)], [('modify_default', None, 'blogs', 'homepage_order', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': '首页显示顺序'}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9dabf6d0>, for_update=False), None), ('modify_comment', None, 'blogs', 'homepage_order', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9dabf6d0>, for_update=False)}, '首页显示顺序', None)], [('modify_comment', None, 'blogs', 'published_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_server_default': False}, '发布时间', None)], ('remove_index', Index('ft_blogs_title_description', Column('title', VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=255), table=<blogs>, nullable=False), Column('description', TEXT(charset='utf8mb4', collation='utf8mb4_unicode_ci'), table=<blogs>))), ('remove_index', Index('idx_article_type_featured', Column('article_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<blogs>, nullable=False, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9dab6c50>, for_update=False)), Column('featured', TINYINT(display_width=1), table=<blogs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf76490>, for_update=False)))), ('remove_index', Index('idx_article_type_open_source', Column('article_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<blogs>, nullable=False, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9dab6c50>, for_update=False)), Column('is_open_source', TINYINT(display_width=1), table=<blogs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf77e30>, for_update=False), comment='是否开源项目'))), ('remove_index', Index('idx_blogs_article_type', Column('article_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<blogs>, nullable=False, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9dab6c50>, for_update=False)))), ('remove_index', Index('idx_blogs_homepage_display', Column('show_on_homepage', TINYINT(display_width=1), table=<blogs>), Column('published', TINYINT(display_width=1), table=<blogs>), Column('article_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<blogs>, nullable=False, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9dab6c50>, for_update=False)), Column('homepage_order', INTEGER(), table=<blogs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9dabf6d0>, for_update=False), comment='首页显示顺序'), Column('display_date', DATETIME(), table=<blogs>, nullable=False, comment='展示日期，用于前端显示和排序'))), ('remove_index', Index('idx_blogs_homepage_order', Column('homepage_order', INTEGER(), table=<blogs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9dabf6d0>, for_update=False), comment='首页显示顺序'), Column('show_on_homepage', TINYINT(display_width=1), table=<blogs>))), ('remove_index', Index('idx_blogs_type_published_homepage', Column('article_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<blogs>, nullable=False, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9dab6c50>, for_update=False)), Column('published', TINYINT(display_width=1), table=<blogs>), Column('show_on_homepage', TINYINT(display_width=1), table=<blogs>))), ('remove_index', Index('idx_featured', Column('featured', TINYINT(display_width=1), table=<blogs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf76490>, for_update=False)))), ('remove_index', Index('idx_is_open_source', Column('is_open_source', TINYINT(display_width=1), table=<blogs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf77e30>, for_update=False), comment='是否开源项目'))), ('remove_index', Index('ix_blogs_date', Column('display_date', DATETIME(), table=<blogs>, nullable=False, comment='展示日期，用于前端显示和排序'))), ('add_index', Index('ix_blogs_article_type', Column('article_type', String(length=50), table=<blogs>, nullable=False, default=ScalarElementColumnDefault('blog')))), ('add_index', Index('ix_blogs_display_date', Column('display_date', DateTime(), table=<blogs>, nullable=False))), ('remove_column', None, 'blogs', Column('has_detail_page', TINYINT(display_width=1), table=<blogs>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bfd00e0>, for_update=False))), ('remove_column', None, 'blogs', Column('tech_stack', JSON(), table=<blogs>, comment='技术栈（JSON数组）')), [('modify_default', None, 'careers', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6b1d0>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x7c6b9dbc2510; now>, for_update=False))], [('modify_default', None, 'careers', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6b530>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x7c6b9dbc2580; now>, for_update=False))], [('modify_default', None, 'content_drafts', 'status', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=20), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf71b50>, for_update=False), None)], [('modify_default', None, 'content_drafts', 'version', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9db7f790>, for_update=False), None)], [('modify_default', None, 'content_drafts', 'auto_save_interval', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9db7f130>, for_update=False), None)], [('modify_default', None, 'content_drafts', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9dad1850>, for_update=False), None)], [('modify_default', None, 'content_drafts', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9dad1650>, for_update=False), None)], ('remove_index', Index('idx_content_type', Column('content_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<content_drafts>, nullable=False))), ('remove_index', Index('idx_created_by', Column('created_by', INTEGER(), table=<content_drafts>, nullable=False))), ('remove_index', Index('idx_status', Column('status', VARCHAR(collation='utf8mb4_unicode_ci', length=20), table=<content_drafts>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf71b50>, for_update=False)))), ('add_index', Index('ix_content_drafts_id', Column('id', Integer(), table=<content_drafts>, primary_key=True, nullable=False))), ('remove_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x7c6b9bba4950>, None, name='content_drafts_ibfk_3', ondelete='CASCADE', table=Table('content_drafts', MetaData(), Column('created_by', NullType(), ForeignKey('users.id'), table=<content_drafts>), schema=None))), ('remove_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x7c6b9bba4900>, None, name='content_drafts_ibfk_2', ondelete='SET NULL', table=Table('content_drafts', MetaData(), Column('parent_draft_id', NullType(), ForeignKey('content_drafts.id'), table=<content_drafts>), Column('id', NullType(), table=<content_drafts>), schema=None))), ('remove_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x7c6b9bba49f0>, None, name='content_drafts_ibfk_1', ondelete='SET NULL', table=Table('content_drafts', MetaData(), Column('template_id', NullType(), ForeignKey('content_templates.id'), table=<content_drafts>), schema=None))), ('add_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x7c6b9bba4a40>, None, table=Table('content_drafts', MetaData(), Column('created_by', NullType(), ForeignKey('users.id'), table=<content_drafts>), schema=None))), ('add_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x7c6b9bba49a0>, None, table=Table('content_drafts', MetaData(), Column('parent_draft_id', NullType(), ForeignKey('content_drafts.id'), table=<content_drafts>), Column('id', NullType(), table=<content_drafts>), schema=None))), ('add_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x7c6b9bba4a90>, None, table=Table('content_drafts', MetaData(), Column('template_id', NullType(), ForeignKey('content_templates.id'), table=<content_drafts>), schema=None))), [('modify_default', None, 'content_templates', 'template_type', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf5a300>, for_update=False), None)], [('modify_default', None, 'content_templates', 'is_active', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf5a3f0>, for_update=False), None)], [('modify_default', None, 'content_templates', 'is_default', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bfa6dd0>, for_update=False), None)], [('modify_default', None, 'content_templates', 'is_system', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bfa7070>, for_update=False), None)], [('modify_default', None, 'content_templates', 'usage_count', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf91b20>, for_update=False), None)], [('modify_default', None, 'content_templates', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9db5ba10>, for_update=False), None)], [('modify_default', None, 'content_templates', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9db5b590>, for_update=False), None)], ('remove_index', Index('idx_is_active', Column('is_active', TINYINT(display_width=1), table=<content_templates>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf5a3f0>, for_update=False)))), ('add_index', Index('ix_content_templates_id', Column('id', Integer(), table=<content_templates>, primary_key=True, nullable=False))), ('remove_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x7c6b9bba4b30>, None, name='content_templates_ibfk_1', ondelete='SET NULL', table=Table('content_templates', MetaData(), Column('created_by', NullType(), ForeignKey('users.id'), table=<content_templates>), schema=None))), ('add_fk', ForeignKeyConstraint(<sqlalchemy.sql.base.ReadOnlyColumnCollection object at 0x7c6b9bba4bd0>, None, table=Table('content_templates', MetaData(), Column('created_by', NullType(), ForeignKey('users.id'), table=<content_templates>), schema=None))), [('modify_default', None, 'educations', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be85070>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x7c6b9dbc2040; now>, for_update=False))], [('modify_default', None, 'educations', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be85310>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x7c6b9dbc20b0; now>, for_update=False))], ('remove_index', Index('idx_category', Column('category_id', INTEGER(), table=<images>, comment='分类ID'))), ('remove_index', Index('idx_display_name', Column('display_name', VARCHAR(collation='utf8mb4_unicode_ci', length=255), table=<images>, comment='显示名称（用户可修改）'))), [('modify_default', None, 'layout_blocks', 'enabled', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be86270>, for_update=False), None)], [('modify_default', None, 'layout_blocks', 'display_order', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be862d0>, for_update=False), None)], [('modify_default', None, 'layout_blocks', 'page_type', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be86330>, for_update=False), None)], [('modify_default', None, 'layout_blocks', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be86390>, for_update=False), None)], [('modify_default', None, 'layout_blocks', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be86690>, for_update=False), None)], ('remove_index', Index('block_id', Column('block_id', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<layout_blocks>, nullable=False), unique=True)), ('remove_index', Index('idx_block_id', Column('block_id', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<layout_blocks>, nullable=False))), ('remove_index', Index('idx_display_order', Column('display_order', INTEGER(), table=<layout_blocks>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be862d0>, for_update=False)))), ('remove_index', Index('idx_page_type', Column('page_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<layout_blocks>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be86330>, for_update=False)))), ('add_index', Index('ix_layout_blocks_block_id', Column('block_id', String(length=100), table=<layout_blocks>, nullable=False), unique=True)), ('add_index', Index('ix_layout_blocks_id', Column('id', Integer(), table=<layout_blocks>, primary_key=True, nullable=False))), [('modify_default', None, 'navigation_items', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be855b0>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x7c6b9dbc2c80; now>, for_update=False))], [('modify_default', None, 'navigation_items', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be85970>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x7c6b9dbc2cf0; now>, for_update=False))], [('modify_default', None, 'page_layouts', 'is_active', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be86870>, for_update=False), None)], [('modify_default', None, 'page_layouts', 'version', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=20), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be86bd0>, for_update=False), None)], [('modify_default', None, 'page_layouts', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be86b70>, for_update=False), None)], [('modify_default', None, 'page_layouts', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be86c30>, for_update=False), None)], ('remove_index', Index('idx_is_active', Column('is_active', TINYINT(display_width=1), table=<page_layouts>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be86870>, for_update=False)))), ('remove_index', Index('idx_page_type', Column('page_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<page_layouts>, nullable=False))), ('remove_index', Index('page_type', Column('page_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<page_layouts>, nullable=False), unique=True)), ('add_index', Index('ix_page_layouts_id', Column('id', Integer(), table=<page_layouts>, primary_key=True, nullable=False))), ('add_index', Index('ix_page_layouts_page_type', Column('page_type', String(length=50), table=<page_layouts>, nullable=False), unique=True)), [('modify_comment', None, 'seo_settings', 'setting_key', {'existing_nullable': False, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=100), 'existing_server_default': False}, 'SEO设置键名', None)], [('modify_comment', None, 'seo_settings', 'setting_value', {'existing_nullable': True, 'existing_type': JSON(), 'existing_server_default': False}, 'SEO设置值（JSON格式）', None)], [('modify_default', None, 'seo_settings', 'setting_type', {'existing_nullable': True, 'existing_type': ENUM('global', 'page', 'content', 'social', 'analytics', collation='utf8mb4_unicode_ci'), 'existing_comment': 'SEO设置类型'}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be85c70>, for_update=False), None), ('modify_comment', None, 'seo_settings', 'setting_type', {'existing_nullable': True, 'existing_type': ENUM('global', 'page', 'content', 'social', 'analytics', collation='utf8mb4_unicode_ci'), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be85c70>, for_update=False)}, 'SEO设置类型', None)], [('modify_comment', None, 'seo_settings', 'page_type', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_server_default': False}, '页面类型（homepage, blog, project, about等）', None)], [('modify_comment', None, 'seo_settings', 'content_id', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_server_default': False}, '内容ID（用于内容级别的SEO设置）', None)], [('modify_nullable', None, 'seo_settings', 'is_active', {'existing_type': TINYINT(display_width=1), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be85cd0>, for_update=False), 'existing_comment': '是否启用'}, True, False), ('modify_default', None, 'seo_settings', 'is_active', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': '是否启用'}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be85cd0>, for_update=False), None), ('modify_comment', None, 'seo_settings', 'is_active', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be85cd0>, for_update=False)}, '是否启用', None)], [('modify_nullable', None, 'seo_settings', 'priority', {'existing_type': INTEGER(), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be86150>, for_update=False), 'existing_comment': '优先级（数字越大优先级越高）'}, True, False), ('modify_default', None, 'seo_settings', 'priority', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': '优先级（数字越大优先级越高）'}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be86150>, for_update=False), None), ('modify_comment', None, 'seo_settings', 'priority', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be86150>, for_update=False)}, '优先级（数字越大优先级越高）', None)], [('modify_comment', None, 'seo_settings', 'description', {'existing_nullable': True, 'existing_type': TEXT(collation='utf8mb4_unicode_ci'), 'existing_server_default': False}, 'SEO设置描述', None)], [('modify_type', None, 'seo_settings', 'created_at', {'existing_nullable': True, 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be860f0>, for_update=False), 'existing_comment': None}, TIMESTAMP(), DateTime()), ('modify_default', None, 'seo_settings', 'created_at', {'existing_nullable': True, 'existing_type': TIMESTAMP(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be860f0>, for_update=False), None)], [('modify_type', None, 'seo_settings', 'updated_at', {'existing_nullable': True, 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be861b0>, for_update=False), 'existing_comment': None}, TIMESTAMP(), DateTime()), ('modify_default', None, 'seo_settings', 'updated_at', {'existing_nullable': True, 'existing_type': TIMESTAMP(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be861b0>, for_update=False), None)], ('remove_index', Index('idx_content_id', Column('content_id', INTEGER(), table=<seo_settings>, comment='内容ID（用于内容级别的SEO设置）'))), ('remove_index', Index('idx_is_active', Column('is_active', TINYINT(display_width=1), table=<seo_settings>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be85cd0>, for_update=False), comment='是否启用'))), ('remove_index', Index('idx_page_type', Column('page_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<seo_settings>, comment='页面类型（homepage, blog, project, about等）'))), ('remove_index', Index('idx_setting_key', Column('setting_key', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<seo_settings>, nullable=False, comment='SEO设置键名'))), ('remove_index', Index('idx_setting_type', Column('setting_type', ENUM('global', 'page', 'content', 'social', 'analytics'), table=<seo_settings>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be85c70>, for_update=False), comment='SEO设置类型'))), ('remove_index', Index('setting_key', Column('setting_key', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<seo_settings>, nullable=False, comment='SEO设置键名'), unique=True)), ('add_index', Index('ix_seo_settings_content_id', Column('content_id', Integer(), table=<seo_settings>))), ('add_index', Index('ix_seo_settings_id', Column('id', Integer(), table=<seo_settings>, primary_key=True, nullable=False))), ('add_index', Index('ix_seo_settings_is_active', Column('is_active', Boolean(), table=<seo_settings>, nullable=False, default=ScalarElementColumnDefault(True)))), ('add_index', Index('ix_seo_settings_page_type', Column('page_type', String(length=50), table=<seo_settings>))), ('add_index', Index('ix_seo_settings_setting_key', Column('setting_key', String(length=100), table=<seo_settings>, nullable=False), unique=True)), ('add_index', Index('ix_seo_settings_setting_type', Column('setting_type', Enum('global', 'page', 'content', 'social', 'analytics'), table=<seo_settings>, default=ScalarElementColumnDefault('global')))), ('remove_table_comment', Table('seo_settings', MetaData(), schema=None)), [('modify_comment', None, 'site_settings', 'setting_key', {'existing_nullable': False, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=100), 'existing_server_default': False}, '设置键名', None)], [('modify_comment', None, 'site_settings', 'setting_value', {'existing_nullable': True, 'existing_type': JSON(), 'existing_server_default': False}, '设置值（JSON格式）', None)], [('modify_default', None, 'site_settings', 'setting_type', {'existing_nullable': True, 'existing_type': ENUM('personal_info', 'theme', 'social_links', 'navigation', 'general', collation='utf8mb4_unicode_ci'), 'existing_comment': '设置类型'}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be859d0>, for_update=False), None), ('modify_comment', None, 'site_settings', 'setting_type', {'existing_nullable': True, 'existing_type': ENUM('personal_info', 'theme', 'social_links', 'navigation', 'general', collation='utf8mb4_unicode_ci'), 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be859d0>, for_update=False)}, '设置类型', None)], [('modify_comment', None, 'site_settings', 'description', {'existing_nullable': True, 'existing_type': TEXT(collation='utf8mb4_unicode_ci'), 'existing_server_default': False}, '设置描述', None)], [('modify_type', None, 'site_settings', 'created_at', {'existing_nullable': True, 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be85a30>, for_update=False), 'existing_comment': None}, TIMESTAMP(), DateTime()), ('modify_default', None, 'site_settings', 'created_at', {'existing_nullable': True, 'existing_type': TIMESTAMP(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be85a30>, for_update=False), None)], [('modify_type', None, 'site_settings', 'updated_at', {'existing_nullable': True, 'existing_server_default': DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be85c10>, for_update=False), 'existing_comment': None}, TIMESTAMP(), DateTime()), ('modify_default', None, 'site_settings', 'updated_at', {'existing_nullable': True, 'existing_type': TIMESTAMP(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be85c10>, for_update=False), None)], ('remove_index', Index('idx_setting_key', Column('setting_key', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<site_settings>, nullable=False, comment='设置键名'))), ('remove_index', Index('idx_setting_type', Column('setting_type', ENUM('personal_info', 'theme', 'social_links', 'navigation', 'general'), table=<site_settings>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be859d0>, for_update=False), comment='设置类型'))), ('remove_index', Index('setting_key', Column('setting_key', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<site_settings>, nullable=False, comment='设置键名'), unique=True)), ('add_index', Index('ix_site_settings_id', Column('id', Integer(), table=<site_settings>, primary_key=True, nullable=False))), ('add_index', Index('ix_site_settings_setting_key', Column('setting_key', String(length=100), table=<site_settings>, nullable=False), unique=True)), ('add_index', Index('ix_site_settings_setting_type', Column('setting_type', Enum('personal_info', 'theme', 'social_links', 'navigation', 'general'), table=<site_settings>, default=ScalarElementColumnDefault('general')))), ('remove_table_comment', Table('site_settings', MetaData(), schema=None)), [('modify_default', None, 'system_metrics_history', 'timestamp', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6a510>, for_update=False), DefaultClause(<sqlalchemy.sql.functions.now at 0x7c6b9dbc3700; now>, for_update=False))], [('modify_type', None, 'system_metrics_history', 'memory_total', {'existing_nullable': True, 'existing_server_default': False, 'existing_comment': None}, BIGINT(), Integer())], [('modify_type', None, 'system_metrics_history', 'memory_used', {'existing_nullable': True, 'existing_server_default': False, 'existing_comment': None}, BIGINT(), Integer())], [('modify_type', None, 'system_metrics_history', 'memory_available', {'existing_nullable': True, 'existing_server_default': False, 'existing_comment': None}, BIGINT(), Integer())], [('modify_type', None, 'system_metrics_history', 'disk_total', {'existing_nullable': True, 'existing_server_default': False, 'existing_comment': None}, BIGINT(), Integer())], [('modify_type', None, 'system_metrics_history', 'disk_used', {'existing_nullable': True, 'existing_server_default': False, 'existing_comment': None}, BIGINT(), Integer())], [('modify_type', None, 'system_metrics_history', 'disk_free', {'existing_nullable': True, 'existing_server_default': False, 'existing_comment': None}, BIGINT(), Integer())], [('modify_default', None, 'tags', 'color', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bfab0e0>, for_update=False), None)], [('modify_comment', None, 'tags', 'icon', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=100), 'existing_server_default': False}, '标签图标，支持统一图标管理系统格式 (library:icon_key)', None)], [('modify_default', None, 'tags', 'category', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bfab4d0>, for_update=False), None)], [('modify_default', None, 'tags', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf69850>, for_update=False), None)], ('remove_index', Index('idx_tags_category', Column('category', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<tags>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bfab4d0>, for_update=False)))), ('remove_index', Index('idx_tags_icon', Column('icon', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<tags>, comment='标签图标，支持统一图标管理系统格式 (library:icon_key)'))), ('remove_index', Index('idx_tags_slug', Column('slug', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<tags>), unique=True)), ('add_index', Index('ix_tags_category', Column('category', String(length=50), table=<tags>, default=ScalarElementColumnDefault('content')))), ('add_index', Index('ix_tags_icon', Column('icon', String(length=100), table=<tags>))), ('add_index', Index('ix_tags_slug', Column('slug', String(length=100), table=<tags>), unique=True)), [('modify_default', None, 'template_categories', 'is_universal', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be848f0>, for_update=False), None)], [('modify_default', None, 'theme_presets', 'is_featured', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6b590>, for_update=False), None)], [('modify_default', None, 'theme_presets', 'is_active', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6b8f0>, for_update=False), None)], [('modify_default', None, 'theme_presets', 'usage_count', {'existing_nullable': True, 'existing_type': INTEGER(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6b890>, for_update=False), None)], [('modify_default', None, 'theme_presets', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6b950>, for_update=False), None)], [('modify_default', None, 'theme_presets', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6b9b0>, for_update=False), None)], ('remove_index', Index('idx_category', Column('category', VARCHAR(collation='utf8mb4_unicode_ci', length=100), table=<theme_presets>))), ('remove_index', Index('idx_is_featured', Column('is_featured', TINYINT(display_width=1), table=<theme_presets>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9bf6b590>, for_update=False)))), ('remove_index', Index('idx_name', Column('name', VARCHAR(collation='utf8mb4_unicode_ci', length=255), table=<theme_presets>, nullable=False))), ('remove_index', Index('name', Column('name', VARCHAR(collation='utf8mb4_unicode_ci', length=255), table=<theme_presets>, nullable=False), unique=True)), ('add_index', Index('ix_theme_presets_id', Column('id', Integer(), table=<theme_presets>, primary_key=True, nullable=False))), ('add_index', Index('ix_theme_presets_name', Column('name', String(length=255), table=<theme_presets>, nullable=False), unique=True)), [('modify_default', None, 'themes', 'theme_type', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=50), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be84890>, for_update=False), None)], [('modify_default', None, 'themes', 'is_active', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be84950>, for_update=False), None)], [('modify_default', None, 'themes', 'is_default', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be84cb0>, for_update=False), None)], [('modify_default', None, 'themes', 'is_public', {'existing_nullable': True, 'existing_type': TINYINT(display_width=1), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be84c50>, for_update=False), None)], [('modify_default', None, 'themes', 'version', {'existing_nullable': True, 'existing_type': VARCHAR(collation='utf8mb4_unicode_ci', length=20), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be84d10>, for_update=False), None)], [('modify_default', None, 'themes', 'created_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be84d70>, for_update=False), None)], [('modify_default', None, 'themes', 'updated_at', {'existing_nullable': True, 'existing_type': DATETIME(), 'existing_comment': None}, DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be84dd0>, for_update=False), None)], ('remove_index', Index('idx_is_active', Column('is_active', TINYINT(display_width=1), table=<themes>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be84950>, for_update=False)))), ('remove_index', Index('idx_name', Column('name', VARCHAR(collation='utf8mb4_unicode_ci', length=255), table=<themes>, nullable=False))), ('remove_index', Index('idx_theme_type', Column('theme_type', VARCHAR(collation='utf8mb4_unicode_ci', length=50), table=<themes>, server_default=DefaultClause(<sqlalchemy.sql.elements.TextClause object at 0x7c6b9be84890>, for_update=False)))), ('remove_index', Index('name', Column('name', VARCHAR(collation='utf8mb4_unicode_ci', length=255), table=<themes>, nullable=False), unique=True)), ('add_index', Index('ix_themes_id', Column('id', Integer(), table=<themes>, primary_key=True, nullable=False))), ('add_index', Index('ix_themes_name', Column('name', String(length=255), table=<themes>, nullable=False), unique=True))]
2025-08-05 14:56:03 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 14:56:03 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 15:01:19 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 15:01:19 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 15:08:58 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 15:08:58 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 15:08:59 INFO  [alembic.runtime.migration] Running upgrade d5ebd9584e9d -> 9639b138f536, stage1_cleanup_unused_tables
2025-08-05 15:10:11 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 15:10:11 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 15:10:11 INFO  [alembic.runtime.migration] Running upgrade 9639b138f536 -> cb351124baaa, stage2_cleanup_broken_views_and_duplicates
2025-08-05 15:10:56 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 15:10:56 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 15:10:56 INFO  [alembic.runtime.migration] Running upgrade 9639b138f536 -> cb351124baaa, stage2_cleanup_broken_views_and_duplicates
2025-08-05 15:11:27 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 15:11:27 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 15:11:27 INFO  [alembic.runtime.migration] Running upgrade 9639b138f536 -> cb351124baaa, stage2_cleanup_broken_views_and_duplicates
2025-08-05 15:12:07 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 15:12:07 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 15:12:07 INFO  [alembic.runtime.migration] Running upgrade 9639b138f536 -> cb351124baaa, stage2_cleanup_broken_views_and_duplicates
2025-08-05 15:14:21 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 15:14:21 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 15:14:21 INFO  [alembic.runtime.migration] Running upgrade cb351124baaa -> 8b45d91a27b8, stage3_cleanup_redundant_fields
2025-08-05 15:15:18 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 15:15:18 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 15:15:18 INFO  [alembic.runtime.migration] Running upgrade 8b45d91a27b8 -> 90f54b9c5e4d, stage4_optimize_table_structure
2025-08-05 17:05:39 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 17:05:39 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 17:06:46 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 17:06:46 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_icon_favorites_id' on 'icon_favorites'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_session_favorite' on 'icon_favorites'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_user_favorite' on 'icon_favorites'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed table 'icon_favorites'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_position'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_style'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_size'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.created_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added column 'albums.type'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added column 'albums.layout_type'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.category'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected type change from VARCHAR(collation='utf8mb4_unicode_ci', length=255) to String(length=100) on 'albums.slug'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.slug'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.is_featured'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.is_featured'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.sort_order'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'albums.sort_order'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_category' on 'albums'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_is_featured' on 'albums'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_is_public' on 'albums'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_public_featured' on 'albums'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_slug' on 'albums'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_sort_order' on 'albums'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added unique constraint None on '('slug',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed column 'albums.is_public'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'api_metrics.timestamp'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'blog_versions.display_date'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected NULL on column 'blog_versions.is_major_change'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'blog_versions.is_major_change'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blog_versions_is_major_change' on 'blog_versions'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.display_date'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.article_type'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.is_github_project'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.is_open_source'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.is_open_source'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.featured'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.display_order'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.homepage_order'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.homepage_order'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.published_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'ft_blogs_title_description' on 'blogs'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_article_type_featured' on 'blogs'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_article_type_open_source' on 'blogs'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_article_type' on 'blogs'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_article_type_published' on 'blogs'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_display_date_published' on 'blogs'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_featured_published' on 'blogs'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_homepage_display' on 'blogs'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_homepage_order' on 'blogs'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_type_published_homepage' on 'blogs'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_featured' on 'blogs'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_open_source' on 'blogs'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_blogs_date' on 'blogs'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_blogs_article_type' on '('article_type',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_blogs_display_date' on '('display_date',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'careers.created_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'careers.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.status'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.version'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.auto_save_interval'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.created_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_content_type' on 'content_drafts'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_created_by' on 'content_drafts'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_status' on 'content_drafts'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_content_drafts_id' on '('id',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed foreign key (created_by)(id) on table content_drafts
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed foreign key (template_id)(id) on table content_drafts
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed foreign key (parent_draft_id)(id) on table content_drafts
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added foreign key (template_id)(id) on table content_drafts
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added foreign key (created_by)(id) on table content_drafts
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added foreign key (parent_draft_id)(id) on table content_drafts
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.template_type'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_active'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_default'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_system'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.usage_count'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.created_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'content_templates'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_content_templates_id' on '('id',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed foreign key (created_by)(id) on table content_templates
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added foreign key (created_by)(id) on table content_templates
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'educations.created_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'educations.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added column 'images.category_id'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added column 'images.upload_source'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added column 'images.external_id'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_display_name' on 'images'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_images_date_created' on 'images'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added foreign key (category_id)(id) on table images
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.enabled'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.display_order'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.page_type'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.created_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'block_id' on 'layout_blocks'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_block_id' on 'layout_blocks'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_display_order' on 'layout_blocks'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'layout_blocks'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_layout_blocks_block_id' on '('block_id',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_layout_blocks_id' on '('id',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'navigation_items.created_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'navigation_items.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.is_active'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.version'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.created_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'page_layouts'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'page_layouts'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'page_type' on 'page_layouts'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_page_layouts_id' on '('id',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_page_layouts_page_type' on '('page_type',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_key'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_value'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.setting_type'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_type'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.page_type'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.content_id'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected NOT NULL on column 'seo_settings.is_active'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.is_active'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.is_active'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected NOT NULL on column 'seo_settings.priority'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.priority'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.priority'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.description'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'seo_settings.created_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.created_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'seo_settings.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_content_id' on 'seo_settings'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'seo_settings'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'seo_settings'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_key' on 'seo_settings'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_type' on 'seo_settings'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'setting_key' on 'seo_settings'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_content_id' on '('content_id',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_id' on '('id',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_is_active' on '('is_active',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_page_type' on '('page_type',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_setting_key' on '('setting_key',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_setting_type' on '('setting_type',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_key'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_value'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.setting_type'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_type'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.description'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'site_settings.created_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.created_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'site_settings.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_key' on 'site_settings'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_type' on 'site_settings'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'setting_key' on 'site_settings'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_id' on '('id',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_setting_key' on '('setting_key',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_setting_type' on '('setting_type',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'system_configs.created_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'system_configs.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'system_metrics_history.timestamp'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.memory_total'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.memory_used'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.memory_available'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.disk_total'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.disk_used'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected type change from BIGINT() to Integer() on 'system_metrics_history.disk_free'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.color'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected column comment 'tags.icon'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.category'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_category' on 'tags'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_category_name' on 'tags'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_icon' on 'tags'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_slug' on 'tags'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_category' on '('category',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_icon' on '('icon',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_slug' on '('slug',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'template_categories.is_universal'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.is_featured'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.is_active'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.usage_count'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.created_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_category' on 'theme_presets'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_featured' on 'theme_presets'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_name' on 'theme_presets'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'theme_presets'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_theme_presets_id' on '('id',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_theme_presets_name' on '('name',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.theme_type'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_active'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_default'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_public'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.version'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.created_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.updated_at'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'themes'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_name' on 'themes'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_theme_type' on 'themes'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'themes'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_themes_id' on '('id',)'
2025-08-05 17:06:46 INFO  [alembic.autogenerate.compare] Detected added index 'ix_themes_name' on '('name',)'
2025-08-05 17:06:58 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 17:06:58 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 17:06:58 INFO  [alembic.runtime.migration] Running upgrade 90f54b9c5e4d -> 0745e82f4e3d, add_missing_fields_to_images_and_albums
2025-08-05 17:07:39 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 17:07:39 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 17:07:39 INFO  [alembic.runtime.migration] Running downgrade 90f54b9c5e4d -> 8b45d91a27b8, stage4_optimize_table_structure
2025-08-05 17:08:13 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-08-05 17:08:13 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-08-05 17:08:13 INFO  [alembic.runtime.migration] Running upgrade 8b45d91a27b8 -> 90f54b9c5e4d, stage4_optimize_table_structure
2025-08-05 17:08:13 INFO  [alembic.runtime.migration] Running upgrade 90f54b9c5e4d -> fix_missing_fields, fix_missing_fields
