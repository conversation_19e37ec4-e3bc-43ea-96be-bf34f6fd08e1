/* 增强图片样式 */

/* 图片容器基础样式 */
.image-container {
  margin: 1.5rem 0;
  display: flex;
  flex-direction: column;
  clear: both;
}

/* 图片对齐方式 */
.image-container.image-left {
  align-items: flex-start;
}

.image-container.image-center {
  align-items: center;
}

.image-container.image-right {
  align-items: flex-end;
}

/* 浮动图片容器 */
.image-float {
  margin: 1rem;
  max-width: 50%;
  display: inline-block;
}

.image-float.image-float-left {
  float: left;
  margin-right: 1.5rem;
  margin-left: 0;
}

.image-float.image-float-right {
  float: right;
  margin-left: 1.5rem;
  margin-right: 0;
}

/* 图片尺寸样式 */
.image-small {
  max-width: 300px;
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.image-medium {
  max-width: 600px;
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.image-large {
  max-width: 900px;
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.image-full {
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* 图片悬停效果 */
.image-small:hover,
.image-medium:hover,
.image-large:hover,
.image-full:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  cursor: pointer;
}

/* 图片说明文字 */
.image-caption {
  margin-top: 0.5rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  color: #666;
  text-align: center;
  font-style: italic;
  line-height: 1.4;
}

.image-float .image-caption {
  text-align: left;
  font-size: 0.8rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-float {
    float: none !important;
    max-width: 100%;
    margin: 1rem 0;
    display: block;
  }
  
  .image-large,
  .image-medium {
    max-width: 100%;
  }
  
  .image-small {
    max-width: 250px;
  }
}

@media (max-width: 480px) {
  .image-small {
    max-width: 200px;
  }
  
  .image-caption {
    font-size: 0.8rem;
  }
}

/* 图片组合展示 */
.image-gallery {
  display: grid;
  gap: 1rem;
  margin: 1.5rem 0;
}

.image-gallery.gallery-2 {
  grid-template-columns: repeat(2, 1fr);
}

.image-gallery.gallery-3 {
  grid-template-columns: repeat(3, 1fr);
}

.image-gallery.gallery-4 {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 768px) {
  .image-gallery.gallery-3,
  .image-gallery.gallery-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .image-gallery.gallery-2,
  .image-gallery.gallery-3,
  .image-gallery.gallery-4 {
    grid-template-columns: 1fr;
  }
}

/* 图片加载动画 */
.image-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 图片错误状态 */
.image-error {
  background-color: #f5f5f5;
  border: 2px dashed #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #999;
  font-size: 0.875rem;
}

/* 图片标题样式 */
.image-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
  font-size: 1rem;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .image-caption {
    color: #999;
  }
  
  .image-title {
    color: #e0e0e0;
  }
  
  .image-error {
    background-color: #2a2a2a;
    border-color: #444;
    color: #666;
  }
}
