import React, { useState, useEffect } from 'react';
import {
  Layout, Card, Row, Col, Button, Input, Select, Space, Tabs,
  Upload, message, Modal, Spin, Empty, Pagination, Tag, Tooltip,
  Dropdown, Menu, Checkbox, Divider, Typography, Statistic
} from 'antd';
import {
  PlusOutlined, UploadOutlined, SearchOutlined, FilterOutlined,
  DeleteOutlined, EditOutlined, EyeOutlined, FolderOutlined,
  PictureOutlined, SettingOutlined, ReloadOutlined, DownloadOutlined,
  TagOutlined, AppstoreOutlined, BarsOutlined, SortAscendingOutlined,
  LinkOutlined, DownOutlined, CloudUploadOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axiosInstance from '../api/axiosInstance';
import ImageUploader from '../components/ImageManager/ImageUploader';
import ImageBedUploader from '../components/ImageManager/ImageBedUploader';
import ImageUrlImporter from '../components/ImageManager/ImageUrlImporter';
import ImageGrid from '../components/ImageManager/ImageGrid';
import ImagePreview from '../components/ImageManager/ImagePreview';
import CategoryManager from '../components/ImageManager/CategoryManager';
import BatchOperations from '../components/ImageManager/BatchOperations';
import ImageFilters from '../components/ImageManager/ImageFilters';
import AdvancedSearch from '../components/ImageManager/AdvancedSearch';
import { showConfirm, confirmDelete } from '../components/common/ConfirmDialog';
import { EnhancedCard, EnhancedButton } from '../components/enhanced';

const { Content, Sider } = Layout;
const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Search } = Input;

interface ImageData {
  id: number;
  url: string;
  thumbnail_url?: string;
  display_name?: string;
  description?: string;
  original_filename: string;
  file_size: number;
  mime_type: string;
  width: number;
  height: number;
  category?: any;
  tags: any[];

  created_at: string;
  view_count: number;
  download_count: number;
}

interface CategoryData {
  id: number;
  name: string;
  slug: string;
  color: string;
  icon?: string;
  image_count: number;
  is_system: boolean;
  is_active: boolean;
  children?: CategoryData[];
}

const ImageManagerPage: React.FC = () => {
  const [selectedImages, setSelectedImages] = useState<number[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [previewImage, setPreviewImage] = useState<ImageData | null>(null);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [imageBedUploadModalVisible, setImageBedUploadModalVisible] = useState(false);
  const [urlImportModalVisible, setUrlImportModalVisible] = useState(false);
  const [categoryModalVisible, setCategoryModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [filters, setFilters] = useState({
    date_from: null,
    date_to: null
  });
  const [advancedFilters, setAdvancedFilters] = useState<any>({});
  const [availableTags, setAvailableTags] = useState<string[]>([]);

  const queryClient = useQueryClient();

  // 获取图片列表
  const { data: imagesData, isLoading: imagesLoading, refetch: refetchImages } = useQuery({
    queryKey: ['images', currentPage, pageSize, searchQuery, selectedCategory, filters],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        page_size: pageSize.toString(),
      });

      if (searchQuery) params.append('query', searchQuery);
      if (selectedCategory) params.append('category_id', selectedCategory.toString());

      if (filters.date_from) params.append('date_from', filters.date_from);
      if (filters.date_to) params.append('date_to', filters.date_to);

      const response = await axiosInstance.post('/images/search', {
        query: searchQuery,
        category_id: selectedCategory,

        date_from: filters.date_from,
        date_to: filters.date_to,
        page: currentPage,
        page_size: pageSize,
        sort_by: 'created_at',
        sort_order: 'desc'
      });
      return response.data;
    }
  });

  // 获取分类列表
  const { data: categories, isLoading: categoriesLoading } = useQuery({
    queryKey: ['image-categories'],
    queryFn: async () => {
      const response = await axiosInstance.get('/image-categories/tree');
      return response.data;
    }
  });

  // 获取可用标签
  const { data: tagsData } = useQuery({
    queryKey: ['image-tags'],
    queryFn: async () => {
      const response = await axiosInstance.get('/images/tags');
      return response.data;
    }
  });

  // 更新可用标签
  useEffect(() => {
    if (tagsData) {
      setAvailableTags(tagsData);
    }
  }, [tagsData]);

  // 获取统计信息
  const { data: stats } = useQuery({
    queryKey: ['image-stats'],
    queryFn: async () => {
      const response = await axiosInstance.get('/image-categories/stats');
      return response.data;
    }
  });

  // 批量删除图片
  const batchDeleteMutation = useMutation({
    mutationFn: async (imageIds: number[]) => {
      const response = await axiosInstance.delete('/images/batch-delete', {
        data: { image_ids: imageIds },
        headers: {
          'Content-Type': 'application/json'
        }
      });
      return response;
    },
    onSuccess: () => {
      message.success('批量删除成功');
      setSelectedImages([]);
      refetchImages();
      queryClient.invalidateQueries({ queryKey: ['image-categories'] });
    },
    onError: (error: any) => {
      message.error(`批量删除失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  // 单个删除图片
  const singleDeleteMutation = useMutation({
    mutationFn: async (imageId: number) => {
      const response = await axiosInstance.delete(`/images/${imageId}?delete_file=true`);
      return response;
    },
    onSuccess: () => {
      message.success('图片删除成功');
      refetchImages();
      queryClient.invalidateQueries({ queryKey: ['image-categories'] });
    },
    onError: (error: any) => {
      message.error(`删除失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  // 批量更新图片
  const batchUpdateMutation = useMutation({
    mutationFn: async (data: any) => {
      await axiosInstance.put('/images/batch-update', data);
    },
    onSuccess: () => {
      message.success('更新成功');
      setSelectedImages([]);
      refetchImages();
      queryClient.invalidateQueries({ queryKey: ['image-categories'] });
    },
    onError: (error: any) => {
      message.error(`更新失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  const handleImageSelect = (imageId: number, selected: boolean) => {
    if (selected) {
      setSelectedImages(prev => [...prev, imageId]);
    } else {
      setSelectedImages(prev => prev.filter(id => id !== imageId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allImageIds = imagesData?.items?.map((img: ImageData) => img.id) || [];
      setSelectedImages(allImageIds);
    } else {
      setSelectedImages([]);
    }
  };

  const handleBatchDelete = () => {
    if (selectedImages.length === 0) {
      message.warning('请选择要删除的图片');
      return;
    }

    confirmDelete({
      title: '⚠️ 危险操作：批量删除图片',
      content: (
        <div>
          <p>您即将删除 <strong style={{color: '#ff4d4f'}}>{selectedImages.length}</strong> 张图片</p>
          <p style={{color: '#ff7875', marginTop: 8}}>
            ⚠️ 此操作将：
          </p>
          <ul style={{color: '#ff7875', marginLeft: 16}}>
            <li>永久删除选中的图片文件</li>
            <li>从数据库中移除图片记录</li>
            <li>无法恢复已删除的数据</li>
          </ul>
          <p style={{marginTop: 12, fontWeight: 'bold'}}>
            确定要继续吗？
          </p>
        </div>
      ),
      onConfirm: () => {
        batchDeleteMutation.mutate(selectedImages);
      }
    });
  };

  const handleBatchCategorize = (categoryId: number) => {
    if (selectedImages.length === 0) {
      message.warning('请选择要分类的图片');
      return;
    }

    batchUpdateMutation.mutate({
      image_ids: selectedImages,
      category_id: categoryId
    });
  };

  const handleBatchAddTags = async (tags: string[]) => {
    if (selectedImages.length === 0) {
      message.warning('请选择要添加标签的图片');
      return;
    }

    try {
      // TODO: 实现批量添加标签API
      console.log('Batch add tags:', selectedImages, tags);
      message.success(`已为 ${selectedImages.length} 张图片添加标签`);
      refetchImages();
    } catch (error) {
      message.error('批量添加标签失败');
    }
  };

  const handleBatchRemoveTags = async (tags: string[]) => {
    if (selectedImages.length === 0) {
      message.warning('请选择要移除标签的图片');
      return;
    }

    try {
      // TODO: 实现批量移除标签API
      console.log('Batch remove tags:', selectedImages, tags);
      message.success(`已从 ${selectedImages.length} 张图片移除标签`);
      refetchImages();
    } catch (error) {
      message.error('批量移除标签失败');
    }
  };

  const handleBatchDownload = async () => {
    if (selectedImages.length === 0) {
      message.warning('请选择要下载的图片');
      return;
    }

    try {
      // TODO: 实现批量下载功能
      console.log('Batch download:', selectedImages);
      message.success(`开始下载 ${selectedImages.length} 张图片`);
    } catch (error) {
      message.error('批量下载失败');
    }
  };

  const handleBatchMove = async (targetCategoryId: number) => {
    if (selectedImages.length === 0) {
      message.warning('请选择要移动的图片');
      return;
    }

    try {
      batchUpdateMutation.mutate({
        image_ids: selectedImages,
        category_id: targetCategoryId
      });
    } catch (error) {
      message.error('批量移动失败');
    }
  };

  const handleBatchReprocess = async () => {
    if (selectedImages.length === 0) {
      message.warning('请选择要重新处理的图片');
      return;
    }

    try {
      // TODO: 实现批量重新处理API
      console.log('Batch reprocess:', selectedImages);
      message.success(`开始重新处理 ${selectedImages.length} 张图片`);
    } catch (error) {
      message.error('批量重新处理失败');
    }
  };



  const handleSearch = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(1);
  };

  const handleAdvancedSearch = (searchFilters: any) => {
    setAdvancedFilters(searchFilters);
    setSearchQuery(searchFilters.query || '');
    setSelectedCategory(searchFilters.category_id || null);
    setCurrentPage(1);

    // 更新基础筛选器
    setFilters({
      date_from: searchFilters.date_from,
      date_to: searchFilters.date_to
    });
  };

  const handleResetSearch = () => {
    setAdvancedFilters({});
    setSearchQuery('');
    setSelectedCategory(null);
    setCurrentPage(1);
    setFilters({
      date_from: null,
      date_to: null
    });
  };

  const handleCategorySelect = (categoryId: number | null) => {
    setSelectedCategory(categoryId);
    setCurrentPage(1);
  };

  const handleFiltersChange = (newFilters: any) => {
    setFilters(newFilters);
    setCurrentPage(1);
  };

  const renderSiderContent = () => (
    <div style={{ padding: '16px' }}>
      {/* 统计信息 */}
      {stats && (
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Statistic
                title="总图片"
                value={stats.total_images || 0}
                prefix={<PictureOutlined />}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title="分类数"
                value={stats.active_categories}
                prefix={<FolderOutlined />}
              />
            </Col>
          </Row>
        </Card>
      )}

      {/* 分类树 */}
      <Card
        title="图片分类"
        size="small"
        extra={
          <Button
            type="text"
            icon={<SettingOutlined />}
            onClick={() => setCategoryModalVisible(true)}
          />
        }
      >
        <div style={{ marginBottom: 8 }}>
          <Button
            type={selectedCategory === null ? 'primary' : 'text'}
            block
            onClick={() => handleCategorySelect(null)}
          >
            全部图片
          </Button>
        </div>
        {categories?.map((category: CategoryData) => (
          <div key={category.id} style={{ marginBottom: 4 }}>
            <Button
              type={selectedCategory === category.id ? 'primary' : 'text'}
              block
              style={{
                textAlign: 'left',
                borderColor: category.color,
                color: selectedCategory === category.id ? '#fff' : category.color
              }}
              onClick={() => handleCategorySelect(category.id)}
            >
              <Space>
                {category.icon && <span>{category.icon}</span>}
                {category.name}
                <Tag color={category.color} size="small">
                  {category.image_count}
                </Tag>
              </Space>
            </Button>
          </div>
        ))}
      </Card>
    </div>
  );

  return (
    <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Sider width={280} style={{ background: '#fff' }}>
        {renderSiderContent()}
      </Sider>

      <Layout>
        <Content style={{ padding: '24px' }}>
          <EnhancedCard variant="elevated" borderAccent style={{ padding: '24px' }}>
            {/* 页面标题和操作栏 */}
            <div style={{ marginBottom: 24 }}>
              <Row justify="space-between" align="middle">
                <Col>
                  <Title level={2} className="page-title" style={{ margin: 0 }}>
                    图片管理
                  </Title>
                </Col>
                <Col>
                  <Space>
                    <Dropdown
                      menu={{
                        items: [
                          {
                            key: 'upload',
                            label: '本地上传',
                            icon: <UploadOutlined />,
                            onClick: () => setUploadModalVisible(true)
                          },
                          {
                            key: 'image-bed-upload',
                            label: '图床上传',
                            icon: <CloudUploadOutlined />,
                            onClick: () => setImageBedUploadModalVisible(true)
                          },
                          {
                            key: 'url-import',
                            label: 'URL导入',
                            icon: <LinkOutlined />,
                            onClick: () => setUrlImportModalVisible(true)
                          }
                        ]
                      }}
                      trigger={['click']}
                    >
                      <EnhancedButton variant="primary" glowing>
                        <Space>
                          添加图片
                          <DownOutlined />
                        </Space>
                      </EnhancedButton>
                    </Dropdown>
                    <EnhancedButton
                      variant="secondary"
                      icon={<ReloadOutlined />}
                      onClick={() => refetchImages()}
                    >
                      刷新
                    </EnhancedButton>
                  </Space>
                </Col>
              </Row>
            </div>

            {/* 高级搜索和筛选 */}
            <div style={{ marginBottom: 16 }}>
              <AdvancedSearch
                categories={categories || []}
                availableTags={availableTags}
                onSearch={handleAdvancedSearch}
                onReset={handleResetSearch}
                loading={imagesLoading}
                initialFilters={{
                  query: searchQuery,
                  category_id: selectedCategory,
                  date_from: filters.date_from,
                  date_to: filters.date_to,
                  ...advancedFilters
                }}
              />
            </div>

            {/* 视图模式切换 */}
            <div style={{ marginBottom: 16, textAlign: 'right' }}>
              <Button.Group>
                <Button
                  type={viewMode === 'grid' ? 'primary' : 'default'}
                  icon={<AppstoreOutlined />}
                  onClick={() => setViewMode('grid')}
                >
                  网格视图
                </Button>
                <Button
                  type={viewMode === 'list' ? 'primary' : 'default'}
                  icon={<BarsOutlined />}
                  onClick={() => setViewMode('list')}
                >
                  列表视图
                </Button>
              </Button.Group>
            </div>

            {/* 批量操作栏 */}
            {selectedImages.length > 0 && (
              <BatchOperations
                selectedCount={selectedImages.length}
                categories={categories}
                onBatchDelete={handleBatchDelete}
                onBatchCategorize={handleBatchCategorize}
                onBatchAddTags={handleBatchAddTags}
                onBatchRemoveTags={handleBatchRemoveTags}
                onBatchDownload={handleBatchDownload}
                onBatchMove={handleBatchMove}
                onBatchReprocess={handleBatchReprocess}
                onClearSelection={() => setSelectedImages([])}
              />
            )}

            {/* 图片网格/列表 */}
            <Spin spinning={imagesLoading}>
              {imagesData?.items?.length > 0 ? (
                <>
                  <div style={{ marginBottom: 16 }}>
                    <Checkbox
                      checked={selectedImages.length === imagesData.items.length}
                      indeterminate={selectedImages.length > 0 && selectedImages.length < imagesData.items.length}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                    >
                      全选 ({imagesData.total} 张图片)
                    </Checkbox>
                  </div>

                  <ImageGrid
                    images={imagesData.items}
                    viewMode={viewMode}
                    selectedImages={selectedImages}
                    onImageSelect={handleImageSelect}
                    onImagePreview={setPreviewImage}
                    onImageEdit={(image) => {
                      // 直接打开预览模式进行编辑
                      setPreviewImage(image);
                    }}
                    onImageDelete={(imageId) => {


                      confirmDelete({
                        title: '⚠️ 确认删除图片',
                        content: (
                          <div>
                            <p>您即将删除这张图片</p>
                            <p style={{color: '#ff7875', marginTop: 8}}>
                              ⚠️ 此操作将：
                            </p>
                            <ul style={{color: '#ff7875', marginLeft: 16}}>
                              <li>永久删除图片文件</li>
                              <li>从数据库中移除图片记录</li>
                              <li>无法恢复已删除的数据</li>
                            </ul>
                            <p style={{marginTop: 12, fontWeight: 'bold'}}>
                              确定要继续吗？
                            </p>
                          </div>
                        ),
                        onConfirm: () => {
                          singleDeleteMutation.mutate(imageId);
                        }
                      });
                    }}
                  />

                  {/* 分页 */}
                  <div style={{ marginTop: 24, textAlign: 'center' }}>
                    <Pagination
                      current={currentPage}
                      pageSize={pageSize}
                      total={imagesData.total}
                      showSizeChanger
                      showQuickJumper
                      showTotal={(total, range) =>
                        `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                      }
                      onChange={(page, size) => {
                        setCurrentPage(page);
                        setPageSize(size || 20);
                      }}
                    />
                  </div>
                </>
              ) : (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无图片"
                  style={{ margin: '40px 0' }}
                >
                  <Space>
                    <EnhancedButton
                      variant="primary"
                      icon={<UploadOutlined />}
                      onClick={() => setUploadModalVisible(true)}
                      glowing
                    >
                      本地上传
                    </EnhancedButton>
                    <EnhancedButton
                      variant="secondary"
                      icon={<LinkOutlined />}
                      onClick={() => setUrlImportModalVisible(true)}
                    >
                      URL导入
                    </EnhancedButton>
                  </Space>
                </Empty>
              )}
            </Spin>
          </EnhancedCard>
        </Content>
      </Layout>

      {/* 上传模态框 */}
      <ImageUploader
        visible={uploadModalVisible}
        onClose={() => setUploadModalVisible(false)}
        onSuccess={() => {
          refetchImages();
          queryClient.invalidateQueries({ queryKey: ['image-categories'] });
        }}
        categories={categories}
      />

      {/* 图床上传模态框 */}
      <ImageBedUploader
        visible={imageBedUploadModalVisible}
        onClose={() => setImageBedUploadModalVisible(false)}
        onSuccess={() => {
          refetchImages();
          queryClient.invalidateQueries({ queryKey: ['image-categories'] });
        }}
        categories={categories}
      />

      {/* URL导入模态框 */}
      <ImageUrlImporter
        visible={urlImportModalVisible}
        onClose={() => setUrlImportModalVisible(false)}
        onSuccess={() => {
          refetchImages();
          queryClient.invalidateQueries({ queryKey: ['image-categories'] });
        }}
        categories={categories}
      />

      {/* 分类管理模态框 */}
      <CategoryManager
        visible={categoryModalVisible}
        onClose={() => setCategoryModalVisible(false)}
        onSuccess={() => {
          queryClient.invalidateQueries({ queryKey: ['image-categories'] });
        }}
      />

      {/* 图片预览模态框 */}
      {previewImage && (
        <ImagePreview
          image={previewImage}
          visible={!!previewImage}
          onClose={() => setPreviewImage(null)}
          onEdit={(image) => {
            // 编辑功能已在ImagePreview组件内部实现
            console.log('Edit image:', image);
          }}
          onDelete={(imageId) => {
            Modal.confirm({
              title: '确认删除',
              content: '确定要删除这张图片吗？此操作不可恢复。',
              okText: '删除',
              okType: 'danger',
              cancelText: '取消',
              onOk: () => {
                singleDeleteMutation.mutate(imageId);
                setPreviewImage(null);
              }
            });
          }}
          categories={categories}
        />
      )}
    </Layout>
  );
};

export default ImageManagerPage;
