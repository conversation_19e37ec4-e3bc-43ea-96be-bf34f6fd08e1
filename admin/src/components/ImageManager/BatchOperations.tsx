import React, { useState } from 'react';
import {
  <PERSON>, Space, Button, Select, Dropdown, Menu, Typography, Tag,
  Divider, Tooltip, Modal, Input, message, Popconfirm
} from 'antd';
import {
  DeleteOutlined, FolderOutlined, TagOutlined, EyeOutlined,
  EyeInvisibleOutlined, DownloadOutlined, CloseOutlined,
  MoreOutlined, ReloadOutlined, SwapOutlined, PlusOutlined,
  MinusOutlined
} from '@ant-design/icons';
import { log } from '../../utils/logger';

const { Text } = Typography;
const { Option } = Select;

interface CategoryData {
  id: number;
  name: string;
  slug: string;
  color: string;
  icon?: string;
  image_count: number;
  is_system: boolean;
  is_active: boolean;
  children?: CategoryData[];
}

interface BatchOperationsProps {
  selectedCount: number;
  categories?: CategoryData[];
  onBatchDelete: () => void;
  onBatchCategorize: (categoryId: number) => void;
  onBatchAddTags: (tags: string[]) => void;
  onBatchRemoveTags: (tags: string[]) => void;
  onBatchDownload: () => void;
  onBatchMove: (targetCategoryId: number) => void;
  onBatchReprocess: () => void;
  onClearSelection: () => void;
}

const BatchOperations: React.FC<BatchOperationsProps> = ({
  selectedCount,
  categories = [],
  onBatchDelete,
  onBatchCategorize,
  onBatchAddTags,
  onBatchRemoveTags,
  onBatchDownload,
  onBatchMove,
  onBatchReprocess,
  onClearSelection
}) => {
  const [tagModalVisible, setTagModalVisible] = useState(false);
  const [tagModalType, setTagModalType] = useState<'add' | 'remove'>('add');
  const [tagInput, setTagInput] = useState('');
  const [moveModalVisible, setMoveModalVisible] = useState(false);

  const handleCategoryChange = (categoryId: number) => {
    onBatchCategorize(categoryId);
  };

  const handleTagOperation = (type: 'add' | 'remove') => {
    setTagModalType(type);
    setTagModalVisible(true);
    setTagInput('');
  };

  const handleTagSubmit = () => {
    if (!tagInput.trim()) {
      message.error('请输入标签');
      return;
    }

    const tags = tagInput.split(',').map(tag => tag.trim()).filter(Boolean);
    if (tags.length === 0) {
      message.error('请输入有效的标签');
      return;
    }

    if (tagModalType === 'add') {
      onBatchAddTags(tags);
      message.success(`已为 ${selectedCount} 张图片添加标签`);
    } else {
      onBatchRemoveTags(tags);
      message.success(`已从 ${selectedCount} 张图片移除标签`);
    }

    setTagModalVisible(false);
    setTagInput('');
  };

  const handleMove = (targetCategoryId: number) => {
    onBatchMove(targetCategoryId);
    setMoveModalVisible(false);
    message.success(`已移动 ${selectedCount} 张图片`);
  };

  const handleReprocess = () => {
    onBatchReprocess();
    message.success(`已开始重新处理 ${selectedCount} 张图片`);
  };

  const handleBatchSetUsageType = (usageType: string) => {
    // TODO: 实现批量设置使用类型
    log.debug('Batch set usage type', { usageType }, 'BATCH_OPS');
  };

  const handleBatchDownload = () => {
    // TODO: 实现批量下载
    log.debug('Batch download', null, 'BATCH_OPS');
  };

  const moreActionsMenu = (
    <Menu>
      <Menu.SubMenu key="visibility" title="设置公开状态" icon={<EyeOutlined />}>
        <Menu.Item
          key="public"
          icon={<EyeOutlined />}
          onClick={() => handleBatchSetPublic(true)}
        >
          设为公开
        </Menu.Item>
        <Menu.Item
          key="private"
          icon={<EyeInvisibleOutlined />}
          onClick={() => handleBatchSetPublic(false)}
        >
          设为私有
        </Menu.Item>
      </Menu.SubMenu>
      
      <Menu.SubMenu key="usage-type" title="设置使用类型" icon={<TagOutlined />}>
        <Menu.Item key="general" onClick={() => handleBatchSetUsageType('general')}>
          通用
        </Menu.Item>
        <Menu.Item key="blog" onClick={() => handleBatchSetUsageType('blog')}>
          博客
        </Menu.Item>
        <Menu.Item key="project" onClick={() => handleBatchSetUsageType('project')}>
          项目
        </Menu.Item>
        <Menu.Item key="gallery" onClick={() => handleBatchSetUsageType('gallery')}>
          相册
        </Menu.Item>
        <Menu.Item key="avatar" onClick={() => handleBatchSetUsageType('avatar')}>
          头像
        </Menu.Item>
        <Menu.Item key="icon" onClick={() => handleBatchSetUsageType('icon')}>
          图标
        </Menu.Item>
      </Menu.SubMenu>
      
      <Menu.Divider />
      
      <Menu.Item
        key="download"
        icon={<DownloadOutlined />}
        onClick={handleBatchDownload}
      >
        批量下载
      </Menu.Item>
    </Menu>
  );

  // 扁平化分类列表
  const flattenCategories = (categories: CategoryData[]): CategoryData[] => {
    const result: CategoryData[] = [];
    const flatten = (cats: CategoryData[], level = 0) => {
      cats.forEach(cat => {
        result.push({ ...cat, level } as CategoryData & { level: number });
        if (cat.children) {
          flatten(cat.children, level + 1);
        }
      });
    };
    flatten(categories);
    return result;
  };

  const allCategories = flattenCategories(categories);

  return (
    <Card
      size="small"
      style={{
        marginBottom: 16,
        borderColor: '#1890ff',
        backgroundColor: '#f6ffed'
      }}
    >
      <Space split={<Divider type="vertical" />} wrap>
        {/* 选中数量 */}
        <Space>
          <Text strong>
            已选中 <Tag color="blue">{selectedCount}</Tag> 张图片
          </Text>
          <Button
            size="small"
            icon={<CloseOutlined />}
            onClick={onClearSelection}
          >
            取消选择
          </Button>
        </Space>

        {/* 批量分类 */}
        <Space>
          <Text>移动到分类：</Text>
          <Select
            placeholder="选择分类"
            style={{ width: 200 }}
            allowClear
            onChange={handleCategoryChange}
            dropdownRender={(menu) => (
              <div>
                {menu}
                <Divider style={{ margin: '8px 0' }} />
                <div style={{ padding: '8px', color: '#999', fontSize: '12px' }}>
                  选择分类后将移动所有选中的图片
                </div>
              </div>
            )}
          >
            <Option value={0}>
              <Space>
                <FolderOutlined />
                未分类
              </Space>
            </Option>
            {allCategories.map(category => (
              <Option key={category.id} value={category.id}>
                <Space>
                  {category.icon && <span>{category.icon}</span>}
                  <span
                    style={{
                      color: category.color,
                      paddingLeft: (category as any).level * 16
                    }}
                  >
                    {category.name}
                  </span>
                  <Tag color={category.color} size="small">
                    {category.image_count}
                  </Tag>
                </Space>
              </Option>
            ))}
          </Select>
        </Space>

        {/* 标签操作 */}
        <Space>
          <Button
            size="small"
            icon={<PlusOutlined />}
            onClick={() => handleTagOperation('add')}
          >
            添加标签
          </Button>
          <Button
            size="small"
            icon={<MinusOutlined />}
            onClick={() => handleTagOperation('remove')}
          >
            移除标签
          </Button>
        </Space>

        {/* 重新处理 */}
        <Tooltip title="重新生成缩略图和WebP格式">
          <Button
            size="small"
            icon={<ReloadOutlined />}
            onClick={handleReprocess}
          >
            重新处理
          </Button>
        </Tooltip>

        {/* 下载 */}
        <Button
          size="small"
          icon={<DownloadOutlined />}
          onClick={onBatchDownload}
        >
          批量下载
        </Button>

        {/* 更多操作 */}
        <Space>
          <Dropdown overlay={moreActionsMenu} trigger={['click']}>
            <Button size="small" icon={<MoreOutlined />}>
              更多操作
            </Button>
          </Dropdown>

          <Popconfirm
            title="确定要删除选中的图片吗？"
            description="此操作不可恢复，请谨慎操作。"
            onConfirm={onBatchDelete}
            okText="删除"
            cancelText="取消"
          >
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              批量删除
            </Button>
          </Popconfirm>
        </Space>
      </Space>

      {/* 标签操作模态框 */}
      <Modal
        title={tagModalType === 'add' ? '批量添加标签' : '批量移除标签'}
        open={tagModalVisible}
        onOk={handleTagSubmit}
        onCancel={() => setTagModalVisible(false)}
        okText="确定"
        cancelText="取消"
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>
            {tagModalType === 'add' ? '为' : '从'} {selectedCount} 张图片
            {tagModalType === 'add' ? '添加' : '移除'}标签：
          </Text>
          <Input.TextArea
            placeholder="请输入标签，多个标签用逗号分隔"
            value={tagInput}
            onChange={(e) => setTagInput(e.target.value)}
            rows={3}
          />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            示例：风景, 自然, 旅行
          </Text>
        </Space>
      </Modal>

      {/* 移动分类模态框 */}
      <Modal
        title="移动到分类"
        open={moveModalVisible}
        onCancel={() => setMoveModalVisible(false)}
        footer={null}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>选择目标分类，将 {selectedCount} 张图片移动到：</Text>
          <Select
            placeholder="选择分类"
            style={{ width: '100%' }}
            onChange={handleMove}
            size="large"
          >
            <Option value={0}>
              <Space>
                <FolderOutlined />
                未分类
              </Space>
            </Option>
            {allCategories.map(category => (
              <Option key={category.id} value={category.id}>
                <Space>
                  {'  '.repeat((category as any).level || 0)}
                  {category.icon && <span>{category.icon}</span>}
                  <span>{category.name}</span>
                  <Tag color={category.color} size="small">
                    {category.image_count}
                  </Tag>
                </Space>
              </Option>
            ))}
          </Select>
        </Space>
      </Modal>
    </Card>
  );
};

export default BatchOperations;
