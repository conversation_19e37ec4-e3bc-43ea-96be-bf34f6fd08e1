import React, { useState, useEffect } from 'react';
import {
  Modal, Input, Select, Row, Col, Card, Button, Space, Empty,
  Pagination, Spin, Image, Typography, Tag, Checkbox, Tabs,
  Upload, message, Form, Radio, Slider, Divider
} from 'antd';
import {
  SearchOutlined, PictureOutlined, UploadOutlined, EyeOutlined,
  CheckOutlined, ReloadOutlined, SettingOutlined, InsertRowBelowOutlined,
  EditOutlined
} from '@ant-design/icons';
import SimpleImageEditor from '../editor/SimpleImageEditor';
import { useQuery } from '@tanstack/react-query';
import axiosInstance from '../../api/axiosInstance';

const { Search } = Input;
const { Text } = Typography;
const { TabPane } = Tabs;

interface ImageData {
  id: number;
  url: string;
  thumbnail_url?: string;
  display_name?: string;
  description?: string;
  original_filename: string;
  file_size: number;
  mime_type: string;
  width: number;
  height: number;
  category?: any;
  tags: any[];
  usage_type: string;
  is_public: boolean;
  created_at: string;
}

interface ImageSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (image: ImageData) => void;
  multiple?: boolean;
  selectedImages?: ImageData[];
  onSelectMultiple?: (images: ImageData[]) => void;
  usageType?: string; // 过滤特定用途的图片
  title?: string;
  // 新增：用于编辑器的增强功能
  forEditor?: boolean;
  onInsertWithOptions?: (imageData: {
    image: ImageData;
    insertType: 'simple' | 'enhanced';
    options?: {
      size?: 'small' | 'medium' | 'large' | 'full';
      align?: 'left' | 'center' | 'right' | 'float-left' | 'float-right';
      caption?: string;
      alt?: string;
      title?: string;
    };
  }) => void;
}

const ImageSelector: React.FC<ImageSelectorProps> = ({
  visible,
  onClose,
  onSelect,
  multiple = false,
  selectedImages = [],
  onSelectMultiple,
  usageType,
  title = '选择图片',
  forEditor = false,
  onInsertWithOptions
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(12);
  const [activeTab, setActiveTab] = useState('browse');
  const [localSelectedImages, setLocalSelectedImages] = useState<ImageData[]>(selectedImages);

  // 编辑器增强功能状态
  const [showInsertOptions, setShowInsertOptions] = useState(false);
  const [selectedImageForInsert, setSelectedImageForInsert] = useState<ImageData | null>(null);
  const [showImageEditor, setShowImageEditor] = useState(false);
  const [selectedImageForEdit, setSelectedImageForEdit] = useState<ImageData | null>(null);
  const [insertOptions, setInsertOptions] = useState({
    size: 'medium' as 'small' | 'medium' | 'large' | 'full',
    align: 'center' as 'left' | 'center' | 'right' | 'float-left' | 'float-right',
    caption: '',
    alt: '',
    title: ''
  });

  // 获取图片列表
  const { data: imagesData, isLoading: imagesLoading, refetch } = useQuery({
    queryKey: ['image-selector', currentPage, pageSize, searchQuery, selectedCategory],
    queryFn: async () => {
      const response = await axiosInstance.post('/images/search', {
        query: searchQuery,
        category_id: selectedCategory,
        page: currentPage,
        page_size: pageSize,
        sort_by: 'created_at',
        sort_order: 'desc'
      });
      return response.data;
    },
    enabled: visible
  });

  // 获取分类列表
  const { data: categories } = useQuery({
    queryKey: ['image-categories-selector'],
    queryFn: async () => {
      const response = await axiosInstance.get('/image-categories/tree');
      return response.data;
    },
    enabled: visible
  });

  useEffect(() => {
    setLocalSelectedImages(selectedImages);
  }, [selectedImages]);

  const getImageUrl = (image: ImageData): string => {
    const baseUrl = 'http://**************:8000';
    return image.thumbnail_url 
      ? `${baseUrl}${image.thumbnail_url}`
      : `${baseUrl}${image.url}`;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleImageClick = (image: ImageData) => {
    if (multiple) {
      const isSelected = localSelectedImages.some(img => img.id === image.id);
      if (isSelected) {
        setLocalSelectedImages(prev => prev.filter(img => img.id !== image.id));
      } else {
        setLocalSelectedImages(prev => [...prev, image]);
      }
    } else if (forEditor && onInsertWithOptions) {
      // 编辑器模式：显示插入选项
      setSelectedImageForInsert(image);
      setInsertOptions(prev => ({
        ...prev,
        alt: image.display_name || image.original_filename,
        title: image.display_name || image.original_filename
      }));
      setShowInsertOptions(true);
    } else {
      onSelect(image);
      onClose();
    }
  };

  const handleConfirmSelection = () => {
    if (multiple && onSelectMultiple) {
      onSelectMultiple(localSelectedImages);
    }
    onClose();
  };

  const handleSearch = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(1);
  };

  const handleCategoryChange = (categoryId: number | null) => {
    setSelectedCategory(categoryId);
    setCurrentPage(1);
  };

  const handleUploadSuccess = () => {
    refetch();
    message.success('图片上传成功');
  };

  // 处理简单插入（直接插入）
  const handleSimpleInsert = (image: ImageData) => {
    if (onInsertWithOptions) {
      onInsertWithOptions({
        image,
        insertType: 'simple'
      });
    } else {
      onSelect(image);
    }
    onClose();
  };

  // 处理增强插入（带选项）
  const handleEnhancedInsert = () => {
    if (selectedImageForInsert && onInsertWithOptions) {
      onInsertWithOptions({
        image: selectedImageForInsert,
        insertType: 'enhanced',
        options: insertOptions
      });
    }
    setShowInsertOptions(false);
    onClose();
  };

  // 取消插入选项
  const handleCancelInsertOptions = () => {
    setShowInsertOptions(false);
    setSelectedImageForInsert(null);
  };

  // 打开图片编辑器
  const handleEditImage = (image: ImageData) => {
    setSelectedImageForEdit(image);
    setShowImageEditor(true);
  };

  // 保存编辑后的图片
  const handleSaveEditedImage = async (editedImageBlob: Blob, originalImage: ImageData) => {
    try {
      // 创建FormData上传编辑后的图片
      const formData = new FormData();
      formData.append('file', editedImageBlob, `edited_${originalImage.original_filename}`);
      formData.append('display_name', `编辑_${originalImage.display_name || originalImage.original_filename}`);
      formData.append('description', `基于 ${originalImage.original_filename} 编辑的图片`);

      const response = await fetch('http://**************:8000/api/images/upload-simple', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + localStorage.getItem('token'),
        },
        body: formData
      });

      if (response.ok) {
        message.success('编辑后的图片已保存');
        refetch(); // 刷新图片列表
        setShowImageEditor(false);
        setSelectedImageForEdit(null);
      } else {
        message.error('保存失败');
      }
    } catch (error) {
      console.error('保存编辑图片失败:', error);
      message.error('保存失败');
    }
  };

  const uploadProps = {
    name: 'file',
    action: 'http://**************:8000/api/images/upload-simple',
    headers: {
      authorization: 'Bearer ' + localStorage.getItem('token'),
    },
    onChange(info: any) {
      if (info.file.status === 'done') {
        handleUploadSuccess();
      } else if (info.file.status === 'error') {
        message.error('图片上传失败');
      }
    },
    showUploadList: false,
    accept: 'image/*',
    beforeUpload: (file: File) => {
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('只能上传图片文件!');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('图片大小不能超过10MB!');
        return false;
      }
      return true;
    }
  };

  const renderImageGrid = () => (
    <div>
      {/* 搜索和过滤 */}
      <div style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col flex="auto">
            <Search
              placeholder="搜索图片..."
              allowClear
              onSearch={handleSearch}
            />
          </Col>
          <Col>
            <Select
              placeholder="选择分类"
              allowClear
              style={{ width: 150 }}
              onChange={handleCategoryChange}
            >
              {categories?.map((category: any) => (
                <Select.Option key={category.id} value={category.id}>
                  <Space>
                    {category.icon && <span>{category.icon}</span>}
                    {category.name}
                  </Space>
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col>
            <Button icon={<ReloadOutlined />} onClick={() => refetch()}>
              刷新
            </Button>
          </Col>
        </Row>
      </div>

      {/* 图片网格 */}
      <Spin spinning={imagesLoading}>
        {imagesData?.items?.length > 0 ? (
          <>
            <Row gutter={[16, 16]}>
              {imagesData.items.map((image: ImageData) => {
                const isSelected = multiple 
                  ? localSelectedImages.some(img => img.id === image.id)
                  : false;

                return (
                  <Col key={image.id} xs={12} sm={8} md={6} lg={4}>
                    <Card
                      hoverable
                      style={{
                        height: '100%',
                        border: isSelected ? '2px solid #1890ff' : '1px solid #d9d9d9'
                      }}
                      cover={
                        <div style={{ position: 'relative', height: 120, overflow: 'hidden' }}>
                          <Image
                            src={getImageUrl(image)}
                            alt={image.display_name || image.original_filename}
                            style={{ 
                              width: '100%', 
                              height: '100%', 
                              objectFit: 'cover',
                              cursor: 'pointer'
                            }}
                            preview={false}
                            onClick={() => handleImageClick(image)}
                          />
                          
                          {/* 选中状态 */}
                          {isSelected && (
                            <div
                              style={{
                                position: 'absolute',
                                top: 8,
                                right: 8,
                                backgroundColor: '#1890ff',
                                borderRadius: '50%',
                                width: 24,
                                height: 24,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            >
                              <CheckOutlined style={{ color: 'white', fontSize: 12 }} />
                            </div>
                          )}

                          {/* 多选模式的复选框 */}
                          {multiple && (
                            <Checkbox
                              checked={isSelected}
                              onChange={() => handleImageClick(image)}
                              style={{
                                position: 'absolute',
                                top: 8,
                                left: 8
                              }}
                            />
                          )}

                          {/* 编辑器模式的快速操作按钮 */}
                          {forEditor && !multiple && (
                            <div
                              style={{
                                position: 'absolute',
                                bottom: 8,
                                right: 8,
                                display: 'flex',
                                gap: 4
                              }}
                            >
                              <Button
                                size="small"
                                type="primary"
                                icon={<InsertRowBelowOutlined />}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleSimpleInsert(image);
                                }}
                                title="快速插入"
                              />
                              <Button
                                size="small"
                                icon={<SettingOutlined />}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleImageClick(image);
                                }}
                                title="插入选项"
                              />
                              <Button
                                size="small"
                                icon={<EditOutlined />}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditImage(image);
                                }}
                                title="编辑图片"
                              />
                            </div>
                          )}
                        </div>
                      }
                      size="small"
                    >
                      <Card.Meta
                        title={
                          <Text ellipsis style={{ fontSize: '12px' }}>
                            {image.display_name || image.original_filename}
                          </Text>
                        }
                        description={
                          <div>
                            <Text type="secondary" style={{ fontSize: '11px' }}>
                              {image.width} × {image.height}
                            </Text>
                            <br />
                            <Text type="secondary" style={{ fontSize: '11px' }}>
                              {formatFileSize(image.file_size)}
                            </Text>
                          </div>
                        }
                      />
                    </Card>
                  </Col>
                );
              })}
            </Row>

            {/* 分页 */}
            <div style={{ marginTop: 16, textAlign: 'center' }}>
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={imagesData.total}
                showSizeChanger={false}
                showQuickJumper
                showTotal={(total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }
                onChange={(page) => setCurrentPage(page)}
              />
            </div>
          </>
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无图片"
            style={{ margin: '40px 0' }}
          />
        )}
      </Spin>
    </div>
  );

  const renderUploadTab = () => (
    <div style={{ textAlign: 'center', padding: '40px 0' }}>
      <Upload.Dragger {...uploadProps}>
        <p className="ant-upload-drag-icon">
          <PictureOutlined />
        </p>
        <p className="ant-upload-text">点击或拖拽图片到此区域上传</p>
        <p className="ant-upload-hint">
          支持 JPG、PNG、GIF、WebP 等格式，文件大小不超过 10MB
        </p>
      </Upload.Dragger>
    </div>
  );

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onClose}
      width={1000}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        multiple && (
          <Button
            key="confirm"
            type="primary"
            onClick={handleConfirmSelection}
            disabled={localSelectedImages.length === 0}
          >
            确定选择 ({localSelectedImages.length})
          </Button>
        )
      ].filter(Boolean)}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="浏览图片" key="browse" icon={<EyeOutlined />}>
          {renderImageGrid()}
        </TabPane>
        <TabPane tab="上传图片" key="upload" icon={<UploadOutlined />}>
          {renderUploadTab()}
        </TabPane>
      </Tabs>

      {/* 选中图片预览 */}
      {multiple && localSelectedImages.length > 0 && (
        <div style={{ marginTop: 16, padding: 16, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
          <Text strong>已选择 {localSelectedImages.length} 张图片：</Text>
          <div style={{ marginTop: 8 }}>
            <Space wrap>
              {localSelectedImages.map(image => (
                <Tag
                  key={image.id}
                  closable
                  onClose={() => {
                    setLocalSelectedImages(prev => prev.filter(img => img.id !== image.id));
                  }}
                >
                  {image.display_name || image.original_filename}
                </Tag>
              ))}
            </Space>
          </div>
        </div>
      )}

      {/* 插入选项模态框 */}
      <Modal
        title="图片插入选项"
        open={showInsertOptions}
        onCancel={handleCancelInsertOptions}
        onOk={handleEnhancedInsert}
        okText="插入图片"
        cancelText="取消"
        width={600}
      >
        {selectedImageForInsert && (
          <div>
            {/* 图片预览 */}
            <div style={{ marginBottom: 24, textAlign: 'center' }}>
              <Image
                src={getImageUrl(selectedImageForInsert)}
                alt={selectedImageForInsert.display_name || selectedImageForInsert.original_filename}
                style={{ maxWidth: '100%', maxHeight: 200 }}
                preview={false}
              />
              <div style={{ marginTop: 8, color: '#666' }}>
                {selectedImageForInsert.display_name || selectedImageForInsert.original_filename}
                <br />
                {selectedImageForInsert.width} × {selectedImageForInsert.height}
              </div>
            </div>

            <Form layout="vertical">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="图片尺寸">
                    <Radio.Group
                      value={insertOptions.size}
                      onChange={(e) => setInsertOptions(prev => ({ ...prev, size: e.target.value }))}
                    >
                      <Radio.Button value="small">小图 (300px)</Radio.Button>
                      <Radio.Button value="medium">中图 (600px)</Radio.Button>
                      <Radio.Button value="large">大图 (900px)</Radio.Button>
                      <Radio.Button value="full">原图</Radio.Button>
                    </Radio.Group>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="对齐方式">
                    <Radio.Group
                      value={insertOptions.align}
                      onChange={(e) => setInsertOptions(prev => ({ ...prev, align: e.target.value }))}
                    >
                      <Radio.Button value="left">左对齐</Radio.Button>
                      <Radio.Button value="center">居中</Radio.Button>
                      <Radio.Button value="right">右对齐</Radio.Button>
                    </Radio.Group>
                    <div style={{ marginTop: 8 }}>
                      <Radio.Group
                        value={insertOptions.align}
                        onChange={(e) => setInsertOptions(prev => ({ ...prev, align: e.target.value }))}
                      >
                        <Radio.Button value="float-left">左浮动</Radio.Button>
                        <Radio.Button value="float-right">右浮动</Radio.Button>
                      </Radio.Group>
                    </div>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item label="图片标题">
                <Input
                  value={insertOptions.title}
                  onChange={(e) => setInsertOptions(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="图片标题（可选）"
                />
              </Form.Item>

              <Form.Item label="替代文本">
                <Input
                  value={insertOptions.alt}
                  onChange={(e) => setInsertOptions(prev => ({ ...prev, alt: e.target.value }))}
                  placeholder="图片的替代文本"
                />
              </Form.Item>

              <Form.Item label="图片说明">
                <Input.TextArea
                  value={insertOptions.caption}
                  onChange={(e) => setInsertOptions(prev => ({ ...prev, caption: e.target.value }))}
                  placeholder="图片说明文字（可选）"
                  rows={2}
                />
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>

      {/* 图片编辑器 */}
      <SimpleImageEditor
        visible={showImageEditor}
        onClose={() => {
          setShowImageEditor(false);
          setSelectedImageForEdit(null);
        }}
        image={selectedImageForEdit}
        onSave={handleSaveEditedImage}
      />
    </Modal>
  );
};

export default ImageSelector;
