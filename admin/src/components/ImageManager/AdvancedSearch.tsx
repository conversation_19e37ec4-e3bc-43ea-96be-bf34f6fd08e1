import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Input, Select, DatePicker, Button, Space, Tag,
  Collapse, Form, InputNumber, Switch, Tooltip, Typography, Divider
} from 'antd';
import {
  SearchOutlined, FilterOutlined, ClearOutlined, SaveOutlined,
  HistoryOutlined, StarOutlined, TagOutlined, CalendarOutlined,
  PictureOutlined, FolderOutlined
} from '@ant-design/icons';
import { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';

const { Search } = Input;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { Panel } = Collapse;
const { Text } = Typography;

interface SearchFilters {
  query: string;
  category_id: number | null;
  tags: string[];
  date_from: string | null;
  date_to: string | null;
  file_type: string | null;
  size_min: number | null;
  size_max: number | null;
  width_min: number | null;
  width_max: number | null;
  height_min: number | null;
  height_max: number | null;
  upload_source: string | null;
  has_thumbnail: boolean | null;
  has_webp: boolean | null;
  sort_by: string;
  sort_order: 'asc' | 'desc';
}

interface AdvancedSearchProps {
  categories?: any[];
  availableTags?: string[];
  onSearch: (filters: SearchFilters) => void;
  onReset: () => void;
  loading?: boolean;
  initialFilters?: Partial<SearchFilters>;
}

const defaultFilters: SearchFilters = {
  query: '',
  category_id: null,
  tags: [],
  date_from: null,
  date_to: null,
  file_type: null,
  size_min: null,
  size_max: null,
  width_min: null,
  width_max: null,
  height_min: null,
  height_max: null,
  upload_source: null,
  has_thumbnail: null,
  has_webp: null,
  sort_by: 'created_at',
  sort_order: 'desc'
};

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  categories = [],
  availableTags = [],
  onSearch,
  onReset,
  loading = false,
  initialFilters = {}
}) => {
  const [filters, setFilters] = useState<SearchFilters>({
    ...defaultFilters,
    ...initialFilters
  });
  const [savedSearches, setSavedSearches] = useState<Array<{
    name: string;
    filters: SearchFilters;
  }>>([]);
  const [showAdvanced, setShowAdvanced] = useState(false);

  // 从localStorage加载保存的搜索
  useEffect(() => {
    const saved = localStorage.getItem('image_saved_searches');
    if (saved) {
      try {
        setSavedSearches(JSON.parse(saved));
      } catch (error) {
        console.error('Failed to load saved searches:', error);
      }
    }
  }, []);

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSearch = () => {
    onSearch(filters);
  };

  const handleReset = () => {
    setFilters(defaultFilters);
    onReset();
  };

  const handleSaveSearch = () => {
    const name = prompt('请输入搜索名称：');
    if (name && name.trim()) {
      const newSavedSearches = [
        ...savedSearches,
        { name: name.trim(), filters: { ...filters } }
      ];
      setSavedSearches(newSavedSearches);
      localStorage.setItem('image_saved_searches', JSON.stringify(newSavedSearches));
    }
  };

  const handleLoadSearch = (savedFilters: SearchFilters) => {
    setFilters(savedFilters);
    onSearch(savedFilters);
  };

  const handleDateRangeChange: RangePickerProps['onChange'] = (dates) => {
    if (dates && dates[0] && dates[1]) {
      setFilters(prev => ({
        ...prev,
        date_from: dates[0].format('YYYY-MM-DD'),
        date_to: dates[1].format('YYYY-MM-DD')
      }));
    } else {
      setFilters(prev => ({
        ...prev,
        date_from: null,
        date_to: null
      }));
    }
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.query) count++;
    if (filters.category_id) count++;
    if (filters.tags.length > 0) count++;
    if (filters.date_from || filters.date_to) count++;
    if (filters.file_type) count++;
    if (filters.size_min || filters.size_max) count++;
    if (filters.width_min || filters.width_max) count++;
    if (filters.height_min || filters.height_max) count++;
    if (filters.upload_source) count++;
    if (filters.has_thumbnail !== null) count++;
    if (filters.has_webp !== null) count++;
    return count;
  };

  return (
    <Card size="small">
      {/* 基础搜索 */}
      <Row gutter={16} align="middle">
        <Col flex="auto">
          <Search
            placeholder="搜索图片名称、描述、文件名、标签..."
            allowClear
            value={filters.query}
            onChange={(e) => handleFilterChange('query', e.target.value)}
            onSearch={handleSearch}
            enterButton={<SearchOutlined />}
            size="large"
          />
        </Col>
        <Col>
          <Select
            placeholder="选择分类"
            allowClear
            value={filters.category_id}
            onChange={(value) => handleFilterChange('category_id', value)}
            style={{ width: 150 }}
          >
            {categories.map(category => (
              <Option key={category.id} value={category.id}>
                <Space>
                  {category.icon && <span>{category.icon}</span>}
                  {category.name}
                </Space>
              </Option>
            ))}
          </Select>
        </Col>
        <Col>
          <Button
            icon={<FilterOutlined />}
            onClick={() => setShowAdvanced(!showAdvanced)}
            type={showAdvanced ? 'primary' : 'default'}
          >
            高级筛选 {getActiveFilterCount() > 0 && `(${getActiveFilterCount()})`}
          </Button>
        </Col>
        <Col>
          <Space>
            <Button
              icon={<SearchOutlined />}
              type="primary"
              onClick={handleSearch}
              loading={loading}
            >
              搜索
            </Button>
            <Button
              icon={<ClearOutlined />}
              onClick={handleReset}
            >
              重置
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 高级搜索面板 */}
      {showAdvanced && (
        <div style={{ marginTop: 16 }}>
          <Divider />
          <Collapse ghost>
            <Panel header="筛选条件" key="filters">
              <Row gutter={[16, 16]}>
                {/* 标签筛选 */}
                <Col span={12}>
                  <Text strong>标签:</Text>
                  <Select
                    mode="multiple"
                    placeholder="选择标签"
                    value={filters.tags}
                    onChange={(value) => handleFilterChange('tags', value)}
                    style={{ width: '100%', marginTop: 8 }}
                  >
                    {availableTags.map(tag => (
                      <Option key={tag} value={tag}>
                        <Tag>{tag}</Tag>
                      </Option>
                    ))}
                  </Select>
                </Col>

                {/* 日期范围 */}
                <Col span={12}>
                  <Text strong>上传日期:</Text>
                  <RangePicker
                    style={{ width: '100%', marginTop: 8 }}
                    onChange={handleDateRangeChange}
                    value={filters.date_from && filters.date_to ? [
                      dayjs(filters.date_from),
                      dayjs(filters.date_to)
                    ] : null}
                  />
                </Col>

                {/* 文件类型 */}
                <Col span={8}>
                  <Text strong>文件类型:</Text>
                  <Select
                    placeholder="选择类型"
                    allowClear
                    value={filters.file_type}
                    onChange={(value) => handleFilterChange('file_type', value)}
                    style={{ width: '100%', marginTop: 8 }}
                  >
                    <Option value="image/jpeg">JPEG</Option>
                    <Option value="image/png">PNG</Option>
                    <Option value="image/gif">GIF</Option>
                    <Option value="image/webp">WebP</Option>
                    <Option value="image/svg+xml">SVG</Option>
                  </Select>
                </Col>

                {/* 上传来源 */}
                <Col span={8}>
                  <Text strong>上传来源:</Text>
                  <Select
                    placeholder="选择来源"
                    allowClear
                    value={filters.upload_source}
                    onChange={(value) => handleFilterChange('upload_source', value)}
                    style={{ width: '100%', marginTop: 8 }}
                  >
                    <Option value="local">本地上传</Option>
                    <Option value="url">URL导入</Option>
                    <Option value="oss">对象存储</Option>
                  </Select>
                </Col>

                {/* 排序 */}
                <Col span={8}>
                  <Text strong>排序方式:</Text>
                  <Space style={{ width: '100%', marginTop: 8 }}>
                    <Select
                      value={filters.sort_by}
                      onChange={(value) => handleFilterChange('sort_by', value)}
                      style={{ flex: 1 }}
                    >
                      <Option value="created_at">上传时间</Option>
                      <Option value="display_name">名称</Option>
                      <Option value="file_size">文件大小</Option>
                      <Option value="width">宽度</Option>
                      <Option value="height">高度</Option>
                    </Select>
                    <Select
                      value={filters.sort_order}
                      onChange={(value) => handleFilterChange('sort_order', value)}
                      style={{ width: 80 }}
                    >
                      <Option value="desc">降序</Option>
                      <Option value="asc">升序</Option>
                    </Select>
                  </Space>
                </Col>
              </Row>
            </Panel>

            <Panel header="尺寸和大小" key="dimensions">
              <Row gutter={[16, 16]}>
                {/* 文件大小 */}
                <Col span={12}>
                  <Text strong>文件大小 (KB):</Text>
                  <Space style={{ width: '100%', marginTop: 8 }}>
                    <InputNumber
                      placeholder="最小"
                      value={filters.size_min}
                      onChange={(value) => handleFilterChange('size_min', value)}
                      min={0}
                    />
                    <Text>-</Text>
                    <InputNumber
                      placeholder="最大"
                      value={filters.size_max}
                      onChange={(value) => handleFilterChange('size_max', value)}
                      min={0}
                    />
                  </Space>
                </Col>

                {/* 图片宽度 */}
                <Col span={12}>
                  <Text strong>宽度 (px):</Text>
                  <Space style={{ width: '100%', marginTop: 8 }}>
                    <InputNumber
                      placeholder="最小"
                      value={filters.width_min}
                      onChange={(value) => handleFilterChange('width_min', value)}
                      min={0}
                    />
                    <Text>-</Text>
                    <InputNumber
                      placeholder="最大"
                      value={filters.width_max}
                      onChange={(value) => handleFilterChange('width_max', value)}
                      min={0}
                    />
                  </Space>
                </Col>

                {/* 图片高度 */}
                <Col span={12}>
                  <Text strong>高度 (px):</Text>
                  <Space style={{ width: '100%', marginTop: 8 }}>
                    <InputNumber
                      placeholder="最小"
                      value={filters.height_min}
                      onChange={(value) => handleFilterChange('height_min', value)}
                      min={0}
                    />
                    <Text>-</Text>
                    <InputNumber
                      placeholder="最大"
                      value={filters.height_max}
                      onChange={(value) => handleFilterChange('height_max', value)}
                      min={0}
                    />
                  </Space>
                </Col>

                {/* 格式选项 */}
                <Col span={12}>
                  <Text strong>格式选项:</Text>
                  <Space direction="vertical" style={{ width: '100%', marginTop: 8 }}>
                    <div>
                      <Switch
                        checked={filters.has_thumbnail === true}
                        onChange={(checked) => handleFilterChange('has_thumbnail', checked ? true : null)}
                      />
                      <span style={{ marginLeft: 8 }}>有缩略图</span>
                    </div>
                    <div>
                      <Switch
                        checked={filters.has_webp === true}
                        onChange={(checked) => handleFilterChange('has_webp', checked ? true : null)}
                      />
                      <span style={{ marginLeft: 8 }}>有WebP格式</span>
                    </div>
                  </Space>
                </Col>
              </Row>
            </Panel>
          </Collapse>

          {/* 保存的搜索 */}
          {savedSearches.length > 0 && (
            <>
              <Divider />
              <div>
                <Text strong>保存的搜索:</Text>
                <div style={{ marginTop: 8 }}>
                  <Space wrap>
                    {savedSearches.map((saved, index) => (
                      <Tag
                        key={index}
                        icon={<StarOutlined />}
                        style={{ cursor: 'pointer' }}
                        onClick={() => handleLoadSearch(saved.filters)}
                      >
                        {saved.name}
                      </Tag>
                    ))}
                  </Space>
                </div>
              </div>
            </>
          )}

          {/* 操作按钮 */}
          <Divider />
          <Space>
            <Button
              icon={<SaveOutlined />}
              onClick={handleSaveSearch}
              disabled={getActiveFilterCount() === 0}
            >
              保存搜索
            </Button>
            <Button
              icon={<SearchOutlined />}
              type="primary"
              onClick={handleSearch}
              loading={loading}
            >
              应用筛选
            </Button>
            <Button
              icon={<ClearOutlined />}
              onClick={handleReset}
            >
              清除所有筛选
            </Button>
          </Space>
        </div>
      )}
    </Card>
  );
};

export default AdvancedSearch;
