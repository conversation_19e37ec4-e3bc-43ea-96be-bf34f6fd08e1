import React, { useState, useRef, useEffect } from 'react';
import { Modal, Button, Slider, Space, Divider, Row, Col, message } from 'antd';
import {
  RotateLeftOutlined, RotateRightOutlined, ScissorOutlined,
  UndoOutlined, RedoOutlined, SaveOutlined, ReloadOutlined
} from '@ant-design/icons';

interface ImageData {
  id: number;
  url: string;
  display_name?: string;
  original_filename: string;
}

interface SimpleImageEditorProps {
  visible: boolean;
  onClose: () => void;
  image: ImageData | null;
  onSave: (editedImageBlob: Blob, originalImage: ImageData) => void;
}

interface ImageFilters {
  brightness: number;
  contrast: number;
  saturation: number;
  blur: number;
  rotation: number;
}

const SimpleImageEditor: React.FC<SimpleImageEditorProps> = ({
  visible,
  onClose,
  image,
  onSave
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [filters, setFilters] = useState<ImageFilters>({
    brightness: 100,
    contrast: 100,
    saturation: 100,
    blur: 0,
    rotation: 0
  });
  const [originalFilters] = useState<ImageFilters>({
    brightness: 100,
    contrast: 100,
    saturation: 100,
    blur: 0,
    rotation: 0
  });
  const [imageLoaded, setImageLoaded] = useState(false);
  const [saving, setSaving] = useState(false);

  // 加载图片到画布
  useEffect(() => {
    if (visible && image && canvasRef.current) {
      loadImageToCanvas();
    }
  }, [visible, image]);

  // 应用滤镜效果
  useEffect(() => {
    if (imageLoaded && canvasRef.current) {
      applyFilters();
    }
  }, [filters, imageLoaded]);

  const loadImageToCanvas = () => {
    if (!image || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      // 设置画布尺寸
      const maxWidth = 600;
      const maxHeight = 400;
      let { width, height } = img;

      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      canvas.width = width;
      canvas.height = height;

      // 绘制原始图片
      ctx.drawImage(img, 0, 0, width, height);
      setImageLoaded(true);
    };

    img.onerror = () => {
      message.error('图片加载失败');
    };

    const baseUrl = 'http://100.90.150.110:8000';
    img.src = `${baseUrl}${image.url}`;
  };

  const applyFilters = () => {
    if (!canvasRef.current || !image) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 重新加载原始图片
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 保存当前状态
      ctx.save();

      // 应用旋转
      if (filters.rotation !== 0) {
        ctx.translate(canvas.width / 2, canvas.height / 2);
        ctx.rotate((filters.rotation * Math.PI) / 180);
        ctx.translate(-canvas.width / 2, -canvas.height / 2);
      }

      // 应用滤镜
      const filterString = [
        `brightness(${filters.brightness}%)`,
        `contrast(${filters.contrast}%)`,
        `saturate(${filters.saturation}%)`,
        `blur(${filters.blur}px)`
      ].join(' ');

      ctx.filter = filterString;

      // 绘制图片
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

      // 恢复状态
      ctx.restore();
    };

    const baseUrl = 'http://100.90.150.110:8000';
    img.src = `${baseUrl}${image.url}`;
  };

  const handleFilterChange = (filterName: keyof ImageFilters, value: number) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
  };

  const handleRotate = (direction: 'left' | 'right') => {
    const rotation = direction === 'left' ? -90 : 90;
    setFilters(prev => ({
      ...prev,
      rotation: (prev.rotation + rotation) % 360
    }));
  };

  const handleReset = () => {
    setFilters({ ...originalFilters });
  };

  const handleSave = async () => {
    if (!canvasRef.current || !image) return;

    setSaving(true);
    try {
      // 将画布内容转换为Blob
      canvasRef.current.toBlob((blob) => {
        if (blob) {
          onSave(blob, image);
          message.success('图片编辑完成');
          onClose();
        } else {
          message.error('保存失败');
        }
        setSaving(false);
      }, 'image/jpeg', 0.9);
    } catch (error) {
      message.error('保存失败');
      setSaving(false);
    }
  };

  const FilterSlider = ({ 
    label, 
    value, 
    min, 
    max, 
    step = 1, 
    suffix = '', 
    onChange 
  }: {
    label: string;
    value: number;
    min: number;
    max: number;
    step?: number;
    suffix?: string;
    onChange: (value: number) => void;
  }) => (
    <Row align="middle" style={{ marginBottom: 12 }}>
      <Col span={6}>
        <span style={{ fontSize: '13px' }}>{label}</span>
      </Col>
      <Col span={14}>
        <Slider
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={onChange}
          tooltip={{ formatter: (val) => `${val}${suffix}` }}
        />
      </Col>
      <Col span={4} style={{ textAlign: 'right' }}>
        <span style={{ fontSize: '12px', color: '#666' }}>
          {value}{suffix}
        </span>
      </Col>
    </Row>
  );

  return (
    <Modal
      title="图片编辑器"
      open={visible}
      onCancel={onClose}
      width={900}
      footer={[
        <Button key="reset" icon={<ReloadOutlined />} onClick={handleReset}>
          重置
        </Button>,
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button
          key="save"
          type="primary"
          icon={<SaveOutlined />}
          loading={saving}
          onClick={handleSave}
        >
          保存编辑
        </Button>
      ]}
    >
      <Row gutter={16}>
        {/* 左侧：图片预览 */}
        <Col span={16}>
          <div style={{ 
            border: '1px solid #d9d9d9', 
            borderRadius: '6px',
            padding: '16px',
            textAlign: 'center',
            backgroundColor: '#fafafa',
            minHeight: '400px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <canvas
              ref={canvasRef}
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                border: '1px solid #e8e8e8',
                borderRadius: '4px',
                backgroundColor: 'white'
              }}
            />
          </div>

          {/* 快速操作按钮 */}
          <div style={{ marginTop: 12, textAlign: 'center' }}>
            <Space>
              <Button
                size="small"
                icon={<RotateLeftOutlined />}
                onClick={() => handleRotate('left')}
              >
                左转90°
              </Button>
              <Button
                size="small"
                icon={<RotateRightOutlined />}
                onClick={() => handleRotate('right')}
              >
                右转90°
              </Button>
            </Space>
          </div>
        </Col>

        {/* 右侧：编辑控制 */}
        <Col span={8}>
          <div style={{ padding: '0 8px' }}>
            <h4 style={{ marginBottom: 16 }}>调整参数</h4>
            
            <FilterSlider
              label="亮度"
              value={filters.brightness}
              min={0}
              max={200}
              suffix="%"
              onChange={(value) => handleFilterChange('brightness', value)}
            />

            <FilterSlider
              label="对比度"
              value={filters.contrast}
              min={0}
              max={200}
              suffix="%"
              onChange={(value) => handleFilterChange('contrast', value)}
            />

            <FilterSlider
              label="饱和度"
              value={filters.saturation}
              min={0}
              max={200}
              suffix="%"
              onChange={(value) => handleFilterChange('saturation', value)}
            />

            <FilterSlider
              label="模糊"
              value={filters.blur}
              min={0}
              max={10}
              step={0.1}
              suffix="px"
              onChange={(value) => handleFilterChange('blur', value)}
            />

            <Divider />

            <div style={{ fontSize: '12px', color: '#666', lineHeight: '1.4' }}>
              <p>💡 提示：</p>
              <ul style={{ paddingLeft: 16, margin: 0 }}>
                <li>调整参数后实时预览效果</li>
                <li>支持旋转和基础滤镜调整</li>
                <li>点击"保存编辑"应用更改</li>
              </ul>
            </div>
          </div>
        </Col>
      </Row>
    </Modal>
  );
};

export default SimpleImageEditor;
