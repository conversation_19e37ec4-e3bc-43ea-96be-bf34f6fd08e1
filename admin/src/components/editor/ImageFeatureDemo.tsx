import React, { useState } from 'react';
import { Card, Button, Typography, Space, Divider, message } from 'antd';
import { CopyOutlined, EyeOutlined } from '@ant-design/icons';
import { BlogContent } from '../blog/BlogContent';

const { Title, Paragraph, Text } = Typography;

const ImageFeatureDemo: React.FC = () => {
  const [previewContent, setPreviewContent] = useState('');
  const [previewVisible, setPreviewVisible] = useState(false);

  const demoExamples = [
    {
      title: '基础图片语法',
      description: '标准的Markdown图片语法，自动应用样式',
      markdown: '![美丽的风景](https://picsum.photos/600/400?random=1)',
      category: 'basic'
    },
    {
      title: '带标题的图片',
      description: '鼠标悬停显示标题',
      markdown: '![风景照片](https://picsum.photos/600/400?random=2 "这是一张美丽的风景照片")',
      category: 'basic'
    },
    {
      title: '小尺寸图片',
      description: '300px宽度的小图片',
      markdown: '![小图片](https://picsum.photos/600/400?random=3){size=small}',
      category: 'enhanced'
    },
    {
      title: '大尺寸居中图片',
      description: '900px宽度的大图片，居中显示',
      markdown: '![大图片](https://picsum.photos/600/400?random=4){size=large,align=center}',
      category: 'enhanced'
    },
    {
      title: '左浮动图片',
      description: '图片浮动在文字左侧，文字环绕显示',
      markdown: `![浮动图片](https://picsum.photos/300/200?random=5){size=small,align=float-left}

这是一段示例文字，用来演示图片浮动效果。当图片设置为左浮动时，文字会自动环绕在图片的右侧显示。这种布局方式特别适合在文章中插入相关的配图，既不会打断阅读流程，又能提供视觉支持。

你可以继续添加更多的文字内容来看到环绕效果。图片会保持在左侧，而文字会在右侧自然流动。这种排版方式在杂志和报纸中经常使用，能够有效利用页面空间。`,
      category: 'enhanced'
    },
    {
      title: '带说明的图片',
      description: '图片下方显示说明文字',
      markdown: '![说明图片](https://picsum.photos/600/400?random=6){size=medium,align=center,caption=这是图片的说明文字，会显示在图片下方}',
      category: 'enhanced'
    },
    {
      title: '组合属性图片',
      description: '同时设置多个属性',
      markdown: '![完整示例](https://picsum.photos/600/400?random=7 "悬停标题"){size=large,align=center,caption=这是一个包含所有属性的完整示例：大尺寸、居中对齐、带标题和说明}',
      category: 'enhanced'
    },
    {
      title: '三列画廊',
      description: '多张图片以网格形式展示',
      markdown: `[gallery:3]
![图片1](https://picsum.photos/400/300?random=8)
![图片2](https://picsum.photos/400/300?random=9)
![图片3](https://picsum.photos/400/300?random=10)
![图片4](https://picsum.photos/400/300?random=11)
![图片5](https://picsum.photos/400/300?random=12)
![图片6](https://picsum.photos/400/300?random=13)
[/gallery]`,
      category: 'gallery'
    },
    {
      title: '两列画廊',
      description: '两列布局的图片画廊',
      markdown: `[gallery:2]
![左图](https://picsum.photos/400/300?random=14 "左侧图片")
![右图](https://picsum.photos/400/300?random=15 "右侧图片")
![下左](https://picsum.photos/400/300?random=16)
![下右](https://picsum.photos/400/300?random=17)
[/gallery]`,
      category: 'gallery'
    },
    {
      title: '图片对比',
      description: '前后对比效果，适合展示变化',
      markdown: `[compare]
![修改前](https://picsum.photos/400/300?random=18)
![修改后](https://picsum.photos/400/300?random=19)
[/compare]`,
      category: 'gallery'
    }
  ];

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    message.success('已复制到剪贴板');
  };

  const previewMarkdown = (markdown: string) => {
    setPreviewContent(markdown);
    setPreviewVisible(true);
  };

  const categories = [
    { key: 'basic', label: '基础语法', color: '#1890ff' },
    { key: 'enhanced', label: '增强语法', color: '#52c41a' },
    { key: 'gallery', label: '画廊模式', color: '#722ed1' }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>图片功能演示</Title>
      <Paragraph>
        以下是所有支持的图片语法示例，你可以复制这些语法到编辑器中使用。
      </Paragraph>

      {categories.map(category => (
        <div key={category.key} style={{ marginBottom: '32px' }}>
          <Title level={3} style={{ color: category.color }}>
            {category.label}
          </Title>
          
          <div style={{ display: 'grid', gap: '16px', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))' }}>
            {demoExamples
              .filter(example => example.category === category.key)
              .map((example, index) => (
                <Card
                  key={index}
                  size="small"
                  title={example.title}
                  extra={
                    <Space>
                      <Button
                        size="small"
                        icon={<EyeOutlined />}
                        onClick={() => previewMarkdown(example.markdown)}
                      >
                        预览
                      </Button>
                      <Button
                        size="small"
                        icon={<CopyOutlined />}
                        onClick={() => copyToClipboard(example.markdown)}
                      >
                        复制
                      </Button>
                    </Space>
                  }
                >
                  <Paragraph style={{ marginBottom: '12px', color: '#666' }}>
                    {example.description}
                  </Paragraph>
                  
                  <div style={{
                    backgroundColor: '#f5f5f5',
                    padding: '12px',
                    borderRadius: '4px',
                    fontFamily: 'monospace',
                    fontSize: '12px',
                    maxHeight: '120px',
                    overflow: 'auto'
                  }}>
                    <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                      {example.markdown}
                    </pre>
                  </div>
                </Card>
              ))}
          </div>
        </div>
      ))}

      {/* 预览模态框 */}
      {previewVisible && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            zIndex: 1000,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '20px'
          }}
          onClick={() => setPreviewVisible(false)}
        >
          <div
            style={{
              backgroundColor: 'white',
              borderRadius: '8px',
              padding: '24px',
              maxWidth: '90%',
              maxHeight: '90%',
              overflow: 'auto'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div style={{ marginBottom: '16px', textAlign: 'right' }}>
              <Button onClick={() => setPreviewVisible(false)}>
                关闭预览
              </Button>
            </div>
            
            <BlogContent content={previewContent} />
          </div>
        </div>
      )}

      <Divider />

      <div style={{ marginTop: '32px', padding: '16px', backgroundColor: '#f0f2f5', borderRadius: '8px' }}>
        <Title level={4}>使用说明</Title>
        <ul>
          <li><strong>基础语法</strong>：标准的Markdown图片语法，自动应用美化样式</li>
          <li><strong>增强语法</strong>：支持尺寸、对齐、说明等属性设置</li>
          <li><strong>画廊模式</strong>：支持多图网格布局和对比展示</li>
          <li><strong>响应式</strong>：所有布局都会在移动端自动适配</li>
          <li><strong>交互效果</strong>：悬停动画、点击放大等增强体验</li>
        </ul>
      </div>
    </div>
  );
};

export default ImageFeatureDemo;
