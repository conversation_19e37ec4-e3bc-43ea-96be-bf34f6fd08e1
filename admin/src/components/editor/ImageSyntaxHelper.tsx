import React, { useState } from 'react';
import { Modal, Button, Tabs, Typography, Card, Space, Divider, Tag } from 'antd';
import { QuestionCircleOutlined, CopyOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

interface ImageSyntaxHelperProps {
  visible: boolean;
  onClose: () => void;
}

const ImageSyntaxHelper: React.FC<ImageSyntaxHelperProps> = ({ visible, onClose }) => {
  const [copiedText, setCopiedText] = useState<string>('');

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopiedText(text);
    setTimeout(() => setCopiedText(''), 2000);
  };

  const SyntaxExample = ({ title, syntax, description, preview }: {
    title: string;
    syntax: string;
    description: string;
    preview?: string;
  }) => (
    <Card size="small" style={{ marginBottom: 16 }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <div style={{ flex: 1 }}>
          <Title level={5} style={{ margin: 0, marginBottom: 8 }}>{title}</Title>
          <Paragraph style={{ margin: 0, marginBottom: 8, color: '#666' }}>
            {description}
          </Paragraph>
          <div style={{ 
            backgroundColor: '#f5f5f5', 
            padding: '8px 12px', 
            borderRadius: '4px',
            fontFamily: 'monospace',
            fontSize: '13px',
            marginBottom: preview ? 8 : 0
          }}>
            {syntax}
          </div>
          {preview && (
            <div style={{ 
              backgroundColor: '#fff', 
              border: '1px solid #e8e8e8',
              padding: '8px 12px', 
              borderRadius: '4px',
              fontSize: '12px',
              color: '#666'
            }}>
              <span style={{ color: '#999' }}>预览效果：</span> {preview}
            </div>
          )}
        </div>
        <Button
          size="small"
          icon={<CopyOutlined />}
          onClick={() => copyToClipboard(syntax)}
          type={copiedText === syntax ? 'primary' : 'default'}
        >
          {copiedText === syntax ? '已复制' : '复制'}
        </Button>
      </div>
    </Card>
  );

  return (
    <Modal
      title="图片插入语法帮助"
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>
      ]}
      width={800}
      style={{ top: 20 }}
    >
      <Tabs defaultActiveKey="basic">
        <TabPane tab="基础语法" key="basic">
          <SyntaxExample
            title="标准图片"
            syntax="![图片描述](图片URL)"
            description="最基本的图片插入语法，图片将以中等尺寸居中显示"
            preview="居中显示的中等尺寸图片"
          />

          <SyntaxExample
            title="带标题的图片"
            syntax='![图片描述](图片URL "图片标题")'
            description="鼠标悬停时显示标题"
            preview="图片带有悬停标题"
          />

          <SyntaxExample
            title="指定尺寸"
            syntax='![图片描述](图片URL){size=large}'
            description="可选尺寸：small(300px), medium(600px), large(900px), full(100%)"
            preview="大尺寸图片"
          />

          <SyntaxExample
            title="指定对齐"
            syntax='![图片描述](图片URL){align=right}'
            description="可选对齐：left, center, right"
            preview="右对齐图片"
          />
        </TabPane>

        <TabPane tab="高级语法" key="advanced">
          <SyntaxExample
            title="浮动图片"
            syntax='![图片描述](图片URL){align=float-left}'
            description="图片浮动在文字左侧或右侧，文字环绕显示"
            preview="文字环绕的浮动图片"
          />

          <SyntaxExample
            title="带说明的图片"
            syntax='![图片描述](图片URL){caption=这是图片说明文字}'
            description="在图片下方显示说明文字"
            preview="图片下方显示说明文字"
          />

          <SyntaxExample
            title="组合属性"
            syntax='![图片描述](图片URL "标题"){size=large,align=center,caption=说明文字}'
            description="可以组合多个属性，用逗号分隔"
            preview="大尺寸居中图片带说明"
          />

          <SyntaxExample
            title="完整示例"
            syntax='![旅行照片](https://example.com/photo.jpg "美丽的风景"){size=large,align=center,caption=这是我在旅行中拍摄的美丽风景照片}'
            description="包含所有属性的完整示例"
            preview="完整的图片展示效果"
          />
        </TabPane>

        <TabPane tab="画廊模式" key="gallery">
          <SyntaxExample
            title="两列画廊"
            syntax={`[gallery:2]
![图片1](URL1)
![图片2](URL2)
![图片3](URL3)
![图片4](URL4)
[/gallery]`}
            description="创建2列的图片画廊，支持2-4列"
            preview="2x2网格布局的图片画廊"
          />

          <SyntaxExample
            title="三列画廊"
            syntax={`[gallery:3]
![图片1](URL1 "标题1")
![图片2](URL2 "标题2")
![图片3](URL3 "标题3")
[/gallery]`}
            description="创建3列的图片画廊，图片可以带标题"
            preview="3列网格布局的图片画廊"
          />

          <SyntaxExample
            title="图片对比"
            syntax={`[compare]
![修改前](before.jpg)
![修改后](after.jpg)
[/compare]`}
            description="创建前后对比效果，适合展示修改前后的差异"
            preview="左右对比的两张图片"
          />
        </TabPane>

        <TabPane tab="使用技巧" key="tips">
          <Card>
            <Title level={4}>📝 编写技巧</Title>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div>
                <Tag color="blue">技巧 1</Tag>
                <span>使用浮动图片时，建议图片尺寸设置为 small 或 medium，避免占用过多空间</span>
              </div>

              <div>
                <Tag color="green">技巧 2</Tag>
                <span>画廊模式适合展示多张相关图片，如旅行照片、作品集等</span>
              </div>

              <div>
                <Tag color="orange">技巧 3</Tag>
                <span>对比模式适合展示设计前后、产品迭代等对比效果</span>
              </div>

              <div>
                <Tag color="purple">技巧 4</Tag>
                <span>图片说明文字支持简单的文本格式，保持简洁明了</span>
              </div>

              <div>
                <Tag color="red">注意</Tag>
                <span>移动端会自动调整布局，多列画廊在小屏幕上会变为单列显示</span>
              </div>
            </Space>
          </Card>

          <Divider />

          <Card>
            <Title level={4}>🎨 样式预览</Title>
            <Paragraph>
              所有图片都会自动应用以下效果：
            </Paragraph>
            <ul>
              <li>圆角边框和阴影效果</li>
              <li>悬停时的轻微上移和阴影加深</li>
              <li>点击放大查看功能</li>
              <li>响应式布局适配</li>
              <li>懒加载优化</li>
            </ul>
          </Card>
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default ImageSyntaxHelper;
