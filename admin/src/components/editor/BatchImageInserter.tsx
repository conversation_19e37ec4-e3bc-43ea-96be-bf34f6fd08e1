import React, { useState } from 'react';
import { Button, Modal, Tooltip, Select, InputNumber, Space, Divider, Typography } from 'antd';
import { AppstoreOutlined, PictureOutlined } from '@ant-design/icons';
import ImageSelector from '../ImageManager/ImageSelector';

const { Text } = Typography;
const { Option } = Select;

interface ImageData {
  id: number;
  url: string;
  thumbnail_url?: string;
  display_name?: string;
  description?: string;
  original_filename: string;
  file_size: number;
  mime_type: string;
  width: number;
  height: number;
  category?: any;
  tags: any[];
  usage_type: string;
  is_public: boolean;
  created_at: string;
}

interface BatchImageInserterProps {
  onInsert: (markdown: string) => void;
  buttonProps?: {
    size?: 'small' | 'middle' | 'large';
    type?: 'default' | 'primary' | 'ghost' | 'dashed' | 'link' | 'text';
    title?: string;
  };
}

const BatchImageInserter: React.FC<BatchImageInserterProps> = ({
  onInsert,
  buttonProps = {}
}) => {
  const [selectorVisible, setSelectorVisible] = useState(false);
  const [optionsVisible, setOptionsVisible] = useState(false);
  const [selectedImages, setSelectedImages] = useState<ImageData[]>([]);
  const [insertType, setInsertType] = useState<'gallery' | 'compare' | 'list'>('gallery');
  const [galleryColumns, setGalleryColumns] = useState(3);

  // 处理图片选择
  const handleImageSelect = (images: ImageData[]) => {
    setSelectedImages(images);
    setSelectorVisible(false);
    if (images.length > 0) {
      setOptionsVisible(true);
    }
  };

  // 生成批量插入的Markdown
  const generateBatchMarkdown = (): string => {
    if (selectedImages.length === 0) return '';

    const baseUrl = 'http://**************:8000';

    switch (insertType) {
      case 'gallery':
        // 画廊模式
        let galleryMarkdown = `[gallery:${galleryColumns}]\n`;
        selectedImages.forEach(image => {
          const imageUrl = `${baseUrl}${image.url}`;
          const alt = image.display_name || image.original_filename;
          const title = image.description || image.display_name || '';
          galleryMarkdown += `![${alt}](${imageUrl}${title ? ` "${title}"` : ''})\n`;
        });
        galleryMarkdown += '[/gallery]\n';
        return galleryMarkdown;

      case 'compare':
        // 对比模式（只取前两张图片）
        if (selectedImages.length < 2) {
          return '对比模式需要至少选择2张图片';
        }
        let compareMarkdown = '[compare]\n';
        selectedImages.slice(0, 2).forEach(image => {
          const imageUrl = `${baseUrl}${image.url}`;
          const alt = image.display_name || image.original_filename;
          compareMarkdown += `![${alt}](${imageUrl})\n`;
        });
        compareMarkdown += '[/compare]\n';
        return compareMarkdown;

      case 'list':
        // 列表模式
        let listMarkdown = '';
        selectedImages.forEach((image, index) => {
          const imageUrl = `${baseUrl}${image.url}`;
          const alt = image.display_name || image.original_filename;
          const title = image.description || '';
          listMarkdown += `![${alt}](${imageUrl}${title ? ` "${title}"` : ''}){size=medium,align=center}\n\n`;
        });
        return listMarkdown;

      default:
        return '';
    }
  };

  // 确认插入
  const handleConfirmInsert = () => {
    const markdown = generateBatchMarkdown();
    if (markdown) {
      onInsert(markdown);
      setOptionsVisible(false);
      setSelectedImages([]);
    }
  };

  // 取消操作
  const handleCancel = () => {
    setOptionsVisible(false);
    setSelectedImages([]);
  };

  return (
    <>
      <Tooltip title={buttonProps.title || "批量插入图片"}>
        <Button
          size={buttonProps.size || "small"}
          type={buttonProps.type || "default"}
          icon={<AppstoreOutlined />}
          onClick={() => setSelectorVisible(true)}
        />
      </Tooltip>

      {/* 图片选择器 */}
      <ImageSelector
        visible={selectorVisible}
        onClose={() => setSelectorVisible(false)}
        onSelect={() => {}} // 不使用单选
        multiple={true}
        selectedImages={selectedImages}
        onSelectMultiple={handleImageSelect}
        title="选择多张图片"
      />

      {/* 批量插入选项 */}
      <Modal
        title="批量图片插入选项"
        open={optionsVisible}
        onCancel={handleCancel}
        onOk={handleConfirmInsert}
        okText="插入图片"
        cancelText="取消"
        width={600}
      >
        <div style={{ marginBottom: 24 }}>
          <Text strong>已选择 {selectedImages.length} 张图片</Text>
        </div>

        <div style={{ marginBottom: 24 }}>
          <Text>插入方式：</Text>
          <Select
            value={insertType}
            onChange={setInsertType}
            style={{ width: 200, marginLeft: 12 }}
          >
            <Option value="gallery">画廊模式</Option>
            <Option value="compare">对比模式</Option>
            <Option value="list">列表模式</Option>
          </Select>
        </div>

        {insertType === 'gallery' && (
          <div style={{ marginBottom: 24 }}>
            <Text>画廊列数：</Text>
            <InputNumber
              min={2}
              max={4}
              value={galleryColumns}
              onChange={(value) => setGalleryColumns(value || 3)}
              style={{ marginLeft: 12 }}
            />
            <Text type="secondary" style={{ marginLeft: 12 }}>
              (2-4列，移动端自动适配)
            </Text>
          </div>
        )}

        {insertType === 'compare' && (
          <div style={{ marginBottom: 24 }}>
            <Text type="secondary">
              对比模式将使用前两张图片创建左右对比效果
            </Text>
          </div>
        )}

        {insertType === 'list' && (
          <div style={{ marginBottom: 24 }}>
            <Text type="secondary">
              列表模式将按顺序垂直排列所有图片
            </Text>
          </div>
        )}

        <Divider />

        {/* 预览效果 */}
        <div>
          <Text strong>预览效果：</Text>
          <div style={{ 
            marginTop: 12,
            padding: 16,
            backgroundColor: '#f5f5f5',
            borderRadius: 6,
            fontFamily: 'monospace',
            fontSize: '12px',
            maxHeight: 200,
            overflow: 'auto'
          }}>
            <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
              {generateBatchMarkdown()}
            </pre>
          </div>
        </div>

        {/* 图片预览 */}
        <div style={{ marginTop: 16 }}>
          <Text strong>选中的图片：</Text>
          <div style={{ 
            marginTop: 8,
            display: 'flex',
            flexWrap: 'wrap',
            gap: 8,
            maxHeight: 120,
            overflow: 'auto'
          }}>
            {selectedImages.map(image => (
              <div
                key={image.id}
                style={{
                  width: 60,
                  height: 60,
                  borderRadius: 4,
                  overflow: 'hidden',
                  border: '1px solid #d9d9d9'
                }}
              >
                <img
                  src={`http://**************:8000${image.thumbnail_url || image.url}`}
                  alt={image.display_name || image.original_filename}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                />
              </div>
            ))}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default BatchImageInserter;
