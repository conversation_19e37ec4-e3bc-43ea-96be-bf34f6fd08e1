import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>dal, Tooltip } from 'antd';
import { PictureOutlined } from '@ant-design/icons';
import ImageSelector from '../ImageManager/ImageSelector';

interface ImageData {
  id: number;
  url: string;
  thumbnail_url?: string;
  display_name?: string;
  description?: string;
  original_filename: string;
  file_size: number;
  mime_type: string;
  width: number;
  height: number;
  category?: any;
  tags: any[];
  usage_type: string;
  is_public: boolean;
  created_at: string;
}

interface EnhancedImageInserterProps {
  onInsert: (markdown: string) => void;
  buttonProps?: {
    size?: 'small' | 'middle' | 'large';
    type?: 'default' | 'primary' | 'ghost' | 'dashed' | 'link' | 'text';
    icon?: React.ReactNode;
    title?: string;
  };
}

const EnhancedImageInserter: React.FC<EnhancedImageInserterProps> = ({
  onInsert,
  buttonProps = {}
}) => {
  const [selectorVisible, setSelectorVisible] = useState(false);

  // 生成增强的Markdown语法
  const generateEnhancedMarkdown = (imageData: {
    image: ImageData;
    insertType: 'simple' | 'enhanced';
    options?: {
      size?: 'small' | 'medium' | 'large' | 'full';
      align?: 'left' | 'center' | 'right' | 'float-left' | 'float-right';
      caption?: string;
      alt?: string;
      title?: string;
    };
  }): string => {
    const { image, insertType, options } = imageData;
    const baseUrl = 'http://**************:8000';
    const imageUrl = `${baseUrl}${image.url}`;

    if (insertType === 'simple') {
      // 简单插入：标准Markdown语法
      const alt = image.display_name || image.original_filename;
      return `![${alt}](${imageUrl})`;
    }

    // 增强插入：使用新的扩展语法
    if (!options) return `![${image.display_name || image.original_filename}](${imageUrl})`;

    const {
      size = 'medium',
      align = 'center',
      caption = '',
      alt = image.display_name || image.original_filename,
      title = ''
    } = options;

    // 构建选项字符串
    const optionParts: string[] = [];
    if (size !== 'medium') optionParts.push(`size=${size}`);
    if (align !== 'center') optionParts.push(`align=${align}`);
    if (caption) optionParts.push(`caption=${caption}`);

    // 生成增强的Markdown语法
    let markdown = `![${alt}](${imageUrl}`;
    if (title) {
      markdown += ` "${title}"`;
    }
    markdown += ')';

    if (optionParts.length > 0) {
      markdown += `{${optionParts.join(',')}}`;
    }

    return markdown + '\n';
  };

  const handleImageInsert = (imageData: {
    image: ImageData;
    insertType: 'simple' | 'enhanced';
    options?: any;
  }) => {
    const markdown = generateEnhancedMarkdown(imageData);
    onInsert(markdown);
    setSelectorVisible(false);
  };

  const handleSimpleSelect = (image: ImageData) => {
    const baseUrl = 'http://**************:8000';
    const imageUrl = `${baseUrl}${image.url}`;
    const alt = image.display_name || image.original_filename;
    onInsert(`![${alt}](${imageUrl})`);
    setSelectorVisible(false);
  };

  return (
    <>
      <Tooltip title={buttonProps.title || "插入图片"}>
        <Button
          size={buttonProps.size || "small"}
          type={buttonProps.type || "default"}
          icon={buttonProps.icon || <PictureOutlined />}
          onClick={() => setSelectorVisible(true)}
        />
      </Tooltip>

      <ImageSelector
        visible={selectorVisible}
        onClose={() => setSelectorVisible(false)}
        onSelect={handleSimpleSelect}
        forEditor={true}
        onInsertWithOptions={handleImageInsert}
        title="选择图片插入"
      />
    </>
  );
};

export default EnhancedImageInserter;
