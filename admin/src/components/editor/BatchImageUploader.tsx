import React, { useState } from 'react';
import { Button, Modal, Upload, message, Progress, List, Typography, Space, Tag } from 'antd';
import { CloudUploadOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';

const { Text } = Typography;
const { Dragger } = Upload;

interface BatchImageUploaderProps {
  visible: boolean;
  onClose: () => void;
  onUploadComplete: (uploadedImages: any[]) => void;
}

interface UploadProgress {
  file: UploadFile;
  progress: number;
  status: 'uploading' | 'done' | 'error';
  response?: any;
}

const BatchImageUploader: React.FC<BatchImageUploaderProps> = ({
  visible,
  onClose,
  onUploadComplete
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, UploadProgress>>({});
  const [uploading, setUploading] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<any[]>([]);

  // 文件选择处理
  const handleFileChange: UploadProps['onChange'] = (info) => {
    let newFileList = [...info.fileList];

    // 限制文件数量
    if (newFileList.length > 20) {
      message.warning('最多只能同时上传20个文件');
      newFileList = newFileList.slice(0, 20);
    }

    // 过滤非图片文件
    newFileList = newFileList.filter(file => {
      if (file.originFileObj) {
        const isImage = file.originFileObj.type.startsWith('image/');
        if (!isImage) {
          message.error(`${file.name} 不是图片文件`);
          return false;
        }
        
        const isLt10M = file.originFileObj.size / 1024 / 1024 < 10;
        if (!isLt10M) {
          message.error(`${file.name} 文件大小超过10MB`);
          return false;
        }
      }
      return true;
    });

    setFileList(newFileList);
  };

  // 开始批量上传
  const handleBatchUpload = async () => {
    if (fileList.length === 0) {
      message.warning('请先选择要上传的图片');
      return;
    }

    setUploading(true);
    const newUploadProgress: Record<string, UploadProgress> = {};
    const uploadResults: any[] = [];

    // 初始化进度状态
    fileList.forEach(file => {
      if (file.uid) {
        newUploadProgress[file.uid] = {
          file,
          progress: 0,
          status: 'uploading'
        };
      }
    });
    setUploadProgress(newUploadProgress);

    // 并发上传（限制并发数为3）
    const uploadPromises = fileList.map(async (file, index) => {
      if (!file.originFileObj || !file.uid) return null;

      try {
        const formData = new FormData();
        formData.append('file', file.originFileObj);
        formData.append('display_name', file.name.replace(/\.[^/.]+$/, ''));
        formData.append('description', `批量上传的图片 - ${file.name}`);

        const response = await fetch('http://**************:8000/api/images/upload-simple', {
          method: 'POST',
          headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('token'),
          },
          body: formData
        });

        if (response.ok) {
          const result = await response.json();
          
          // 更新进度
          setUploadProgress(prev => ({
            ...prev,
            [file.uid!]: {
              ...prev[file.uid!],
              progress: 100,
              status: 'done',
              response: result
            }
          }));

          return result;
        } else {
          throw new Error('上传失败');
        }
      } catch (error) {
        // 更新错误状态
        setUploadProgress(prev => ({
          ...prev,
          [file.uid!]: {
            ...prev[file.uid!],
            progress: 0,
            status: 'error'
          }
        }));
        
        message.error(`${file.name} 上传失败`);
        return null;
      }
    });

    // 等待所有上传完成
    const results = await Promise.all(uploadPromises);
    const successResults = results.filter(result => result !== null);
    
    setUploadedImages(successResults);
    setUploading(false);

    if (successResults.length > 0) {
      message.success(`成功上传 ${successResults.length} 张图片`);
    }
  };

  // 移除文件
  const handleRemoveFile = (file: UploadFile) => {
    const newFileList = fileList.filter(item => item.uid !== file.uid);
    setFileList(newFileList);
    
    // 移除进度记录
    if (file.uid) {
      const newProgress = { ...uploadProgress };
      delete newProgress[file.uid];
      setUploadProgress(newProgress);
    }
  };

  // 完成上传并关闭
  const handleComplete = () => {
    onUploadComplete(uploadedImages);
    handleClose();
  };

  // 关闭并重置
  const handleClose = () => {
    setFileList([]);
    setUploadProgress({});
    setUploadedImages([]);
    setUploading(false);
    onClose();
  };

  // 获取上传状态统计
  const getUploadStats = () => {
    const total = fileList.length;
    const completed = Object.values(uploadProgress).filter(p => p.status === 'done').length;
    const failed = Object.values(uploadProgress).filter(p => p.status === 'error').length;
    return { total, completed, failed };
  };

  const stats = getUploadStats();

  return (
    <Modal
      title="批量图片上传"
      open={visible}
      onCancel={handleClose}
      width={800}
      footer={[
        <Button key="cancel" onClick={handleClose} disabled={uploading}>
          取消
        </Button>,
        <Button
          key="upload"
          type="primary"
          onClick={handleBatchUpload}
          loading={uploading}
          disabled={fileList.length === 0}
        >
          开始上传 ({fileList.length})
        </Button>,
        uploadedImages.length > 0 && (
          <Button
            key="complete"
            type="primary"
            onClick={handleComplete}
            disabled={uploading}
          >
            完成并使用 ({uploadedImages.length})
          </Button>
        )
      ].filter(Boolean)}
    >
      {/* 文件拖拽上传区域 */}
      <Dragger
        multiple
        accept="image/*"
        fileList={fileList}
        onChange={handleFileChange}
        beforeUpload={() => false} // 阻止自动上传
        showUploadList={false}
        disabled={uploading}
      >
        <p className="ant-upload-drag-icon">
          <CloudUploadOutlined />
        </p>
        <p className="ant-upload-text">点击或拖拽图片到此区域</p>
        <p className="ant-upload-hint">
          支持批量上传，单个文件不超过10MB，最多20个文件
        </p>
      </Dragger>

      {/* 上传状态统计 */}
      {(fileList.length > 0 || uploading) && (
        <div style={{ margin: '16px 0', padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
          <Space>
            <Text>总计: {stats.total}</Text>
            <Text type="success">已完成: {stats.completed}</Text>
            {stats.failed > 0 && <Text type="danger">失败: {stats.failed}</Text>}
          </Space>
        </div>
      )}

      {/* 文件列表 */}
      {fileList.length > 0 && (
        <div style={{ marginTop: 16, maxHeight: 300, overflow: 'auto' }}>
          <List
            size="small"
            dataSource={fileList}
            renderItem={(file) => {
              const progress = file.uid ? uploadProgress[file.uid] : null;
              
              return (
                <List.Item
                  actions={[
                    <Button
                      key="remove"
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={() => handleRemoveFile(file)}
                      disabled={uploading}
                      danger
                    />
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <Text ellipsis style={{ maxWidth: 200 }}>
                          {file.name}
                        </Text>
                        {progress && (
                          <Tag color={
                            progress.status === 'done' ? 'success' :
                            progress.status === 'error' ? 'error' : 'processing'
                          }>
                            {progress.status === 'done' ? '完成' :
                             progress.status === 'error' ? '失败' : '上传中'}
                          </Tag>
                        )}
                      </Space>
                    }
                    description={
                      <div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {file.size ? `${(file.size / 1024 / 1024).toFixed(2)} MB` : ''}
                        </Text>
                        {progress && progress.status === 'uploading' && (
                          <Progress
                            percent={progress.progress}
                            size="small"
                            style={{ marginTop: 4 }}
                          />
                        )}
                      </div>
                    }
                  />
                </List.Item>
              );
            }}
          />
        </div>
      )}

      {/* 上传成功的图片预览 */}
      {uploadedImages.length > 0 && (
        <div style={{ marginTop: 16 }}>
          <Text strong>上传成功的图片：</Text>
          <div style={{ 
            marginTop: 8,
            display: 'flex',
            flexWrap: 'wrap',
            gap: 8,
            maxHeight: 120,
            overflow: 'auto'
          }}>
            {uploadedImages.map((image, index) => (
              <div
                key={index}
                style={{
                  width: 60,
                  height: 60,
                  borderRadius: 4,
                  overflow: 'hidden',
                  border: '1px solid #52c41a'
                }}
              >
                <img
                  src={`http://**************:8000${image.url}`}
                  alt={`上传成功 ${index + 1}`}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </Modal>
  );
};

export default BatchImageUploader;
